# 攻击描述嵌入使用指南

## 📋 概述

本目录包含用于Transformer模型的攻击类型语义嵌入文件和工具，专门为零样本学习零日攻击检测项目设计。

## 📁 文件说明

### 1. `attack_descriptions_for_embedding.csv`
**用途**: Transformer模型输入的攻击类型描述数据
**格式**: CSV文件，包含以下字段：
- `attack_type`: 攻击类型名称
- `short_description`: 简短描述
- `detailed_description`: 详细描述  
- `behavioral_patterns`: 行为模式特征
- `technical_characteristics`: 技术特征

**包含的攻击类型**:
- Normal (正常流量)
- Exploits (漏洞利用)
- Fuzzers (模糊测试)
- DoS (拒绝服务)
- Reconnaissance (侦察)
- Analysis (分析攻击)
- Backdoor (后门)
- Shellcode (外壳代码)
- Worms (蠕虫)
- Generic (通用攻击)

### 2. `attack_description_embedding.py`
**用途**: 完整的攻击描述嵌入生成和分析工具
**功能**:
- 加载攻击描述CSV文件
- 使用预训练Transformer模型生成嵌入
- 计算攻击类型相似度矩阵
- 保存和加载嵌入向量

### 3. `transformer_embedding_example.py`
**用途**: 简化的使用示例和演示
**功能**:
- 基本的嵌入生成流程
- 相似度分析和可视化
- 零样本分类演示
- 模型集成示例

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install pandas numpy torch transformers sentence-transformers scikit-learn matplotlib seaborn
```

### 2. 基本使用
```python
# 运行完整演示
python transformer_embedding_example.py

# 或使用完整工具类
from attack_description_embedding import AttackDescriptionEmbedder

embedder = AttackDescriptionEmbedder()
embedder.load_attack_descriptions()
embeddings = embedder.generate_embeddings()
```

### 3. 集成到您的模型
```python
import pandas as pd
from sentence_transformers import SentenceTransformer

# 加载攻击描述
df = pd.read_csv('attack_descriptions_for_embedding.csv')

# 生成嵌入
model = SentenceTransformer('all-MiniLM-L6-v2')
for _, row in df.iterrows():
    attack_type = row['attack_type']
    description = row['detailed_description']
    embedding = model.encode(description)
    # 在您的模型中使用embedding
```

## 🎯 在零样本学习中的应用

### 1. 语义先验知识
使用攻击描述嵌入为模型提供攻击类型的语义先验知识：

```python
# 为每个攻击类型创建语义表示
attack_embeddings = {
    'Exploits': model.encode("Vulnerability exploitation attacks..."),
    'DoS': model.encode("Denial of Service attacks..."),
    # ... 其他攻击类型
}

# 在零样本分类中使用
def zero_shot_classify(network_features, attack_embeddings):
    # 将网络特征与攻击语义嵌入结合
    # 进行零样本分类
    pass
```

### 2. 相似度引导学习
利用攻击类型之间的语义相似度指导模型学习：

```python
# 计算攻击类型相似度矩阵
similarity_matrix = cosine_similarity(list(attack_embeddings.values()))

# 在损失函数中使用相似度信息
def similarity_guided_loss(predictions, true_labels, similarity_matrix):
    # 考虑攻击类型相似度的损失函数
    pass
```

## 📊 实验结果分析

根据您的模型表现，不同攻击类型的检测难度与其语义特征相关：

### 高检测率攻击类型 (Z-DR > 95%)
- **Worms (100.00%)**: 自动化传播特征明显
- **Backdoor (99.96%)**: 隐蔽通信模式特征突出
- **DoS (99.33%)**: 流量异常特征显著

### 中等检测率攻击类型 (80% < Z-DR < 95%)
- **Shellcode (95.30%)**: 代码注入特征相对明显
- **Generic (93.37%)**: 特征分散但仍可识别

### 低检测率攻击类型 (Z-DR < 50%)
- **Fuzzers (20.01%)**: 随机性强，语义特征模糊

## 💡 优化建议

### 1. 针对低检测率攻击类型
对于Fuzzers等检测困难的攻击，可以：
- 增强描述的技术细节
- 添加更多行为模式特征
- 使用多模态嵌入（文本+数值特征）

### 2. 模型集成策略
```python
# 结合语义嵌入和数值特征
def enhanced_zero_shot_model(network_features, attack_embeddings):
    # 1. 提取网络流量数值特征
    numerical_features = extract_numerical_features(network_features)
    
    # 2. 生成攻击类型语义嵌入
    semantic_features = get_attack_embeddings(attack_embeddings)
    
    # 3. 多模态融合
    combined_features = combine_features(numerical_features, semantic_features)
    
    # 4. 零样本分类
    return zero_shot_classify(combined_features)
```

### 3. 动态描述更新
根据新发现的攻击模式动态更新攻击描述：
```python
def update_attack_descriptions(new_attack_data):
    # 分析新攻击数据
    # 更新攻击描述
    # 重新生成嵌入向量
    pass
```

## 🔧 自定义配置

### 1. 更换嵌入模型
```python
# 使用不同的预训练模型
embedder = AttackDescriptionEmbedder(model_name='roberta-base')
# 或
embedder = AttackDescriptionEmbedder(model_name='sentence-transformers/all-mpnet-base-v2')
```

### 2. 自定义描述字段
修改CSV文件添加新的描述字段，或在代码中选择特定字段：
```python
# 只使用技术特征描述
embeddings = embedder.generate_embeddings(use_field='technical_characteristics')
```

## 📈 性能监控

### 1. 嵌入质量评估
```python
# 评估嵌入向量的聚类质量
from sklearn.metrics import silhouette_score

embeddings_array = np.array(list(embeddings.values()))
labels = list(range(len(embeddings)))
score = silhouette_score(embeddings_array, labels)
print(f"嵌入质量分数: {score}")
```

### 2. 相似度分析
定期分析攻击类型相似度，识别可能的分类混淆：
```python
similarity_df = embedder.get_attack_similarity_matrix()
high_similarity_pairs = find_high_similarity_pairs(similarity_df, threshold=0.8)
```

## 🎉 总结

这套攻击描述嵌入工具为您的零样本学习项目提供了：

1. **语义先验知识**: 丰富的攻击类型描述
2. **可扩展框架**: 易于添加新攻击类型
3. **分析工具**: 相似度分析和可视化
4. **集成接口**: 便于与现有模型集成

通过将语义信息与数值特征结合，可以显著提升零日攻击检测的性能，特别是对于特征不明显的攻击类型。
