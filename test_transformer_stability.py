#!/usr/bin/env python3
"""
测试Transformer数值稳定性的脚本
"""

import numpy as np
import torch
from models import TransformerNIDS

def test_gradient_stability():
    """测试梯度稳定性"""
    print("🔬 测试Transformer梯度稳定性")
    print("="*60)
    
    # 创建各种测试数据
    test_cases = [
        {
            'name': '正常数据',
            'X': np.random.randn(1000, 41).astype(np.float32),
            'y': np.random.randint(0, 2, 1000)
        },
        {
            'name': '极值数据',
            'X': np.concatenate([
                np.random.randn(800, 41).astype(np.float32),
                np.full((100, 41), 1e3, dtype=np.float32),  # 大值
                np.full((100, 41), -1e3, dtype=np.float32)  # 小值
            ]),
            'y': np.random.randint(0, 2, 1000)
        },
        {
            'name': '包含零值',
            'X': np.concatenate([
                np.random.randn(700, 41).astype(np.float32),
                np.zeros((300, 41), dtype=np.float32)  # 零值
            ]),
            'y': np.random.randint(0, 2, 1000)
        },
        {
            'name': '不平衡数据',
            'X': np.random.randn(1000, 41).astype(np.float32),
            'y': np.concatenate([np.ones(950), np.zeros(50)]).astype(int)  # 95%:5%
        }
    ]
    
    results = {}
    
    for test_case in test_cases:
        print(f"\n📊 测试: {test_case['name']}")
        print("-" * 40)
        
        # 创建模型
        model = TransformerNIDS(
            input_size=41,
            d_model=64,  # 确保能被nhead整除
            nhead=8,
            num_layers=2,
            use_gpu=True,
            fast_mode=True
        )
        
        model.epochs = 10
        
        try:
            # 训练并监控
            print(f"   数据形状: {test_case['X'].shape}")
            print(f"   标签分布: {np.bincount(test_case['y'])}")
            
            training_results = model.train(test_case['X'], test_case['y'], verbose=False)
            
            # 测试预测
            predictions = model.predict(test_case['X'][:100])
            probabilities = model.predict_proba(test_case['X'][:100])
            
            # 检查结果
            nan_predictions = np.isnan(predictions).sum()
            nan_probabilities = np.isnan(probabilities).sum()
            
            results[test_case['name']] = {
                'success': True,
                'training_time': training_results['training_time'],
                'final_accuracy': training_results['training_accuracy'],
                'epochs_completed': training_results['epochs_completed'],
                'nan_predictions': nan_predictions,
                'nan_probabilities': nan_probabilities
            }
            
            print(f"   ✅ 训练成功")
            print(f"   训练时间: {training_results['training_time']:.2f}秒")
            print(f"   最终准确率: {training_results['training_accuracy']:.4f}")
            print(f"   完成轮数: {training_results['epochs_completed']}")
            print(f"   NaN预测数: {nan_predictions}")
            print(f"   NaN概率数: {nan_probabilities}")
            
        except Exception as e:
            results[test_case['name']] = {
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ 训练失败: {e}")
    
    return results

def test_different_architectures():
    """测试不同架构配置"""
    print("\n🏗️ 测试不同架构配置")
    print("="*60)
    
    # 创建测试数据
    X_test = np.random.randn(500, 41).astype(np.float32)
    y_test = np.random.randint(0, 2, 500)
    
    architectures = [
        {'d_model': 32, 'nhead': 4, 'num_layers': 2, 'name': '小模型'},
        {'d_model': 64, 'nhead': 8, 'num_layers': 2, 'name': '中模型'},
        {'d_model': 128, 'nhead': 8, 'num_layers': 4, 'name': '大模型'},
        {'d_model': 96, 'nhead': 6, 'num_layers': 3, 'name': '不规则模型'},  # 测试维度调整
    ]
    
    results = {}
    
    for arch in architectures:
        print(f"\n📊 测试{arch['name']}: d_model={arch['d_model']}, nhead={arch['nhead']}")
        
        try:
            model = TransformerNIDS(
                input_size=41,
                d_model=arch['d_model'],
                nhead=arch['nhead'],
                num_layers=arch['num_layers'],
                use_gpu=True,
                fast_mode=True
            )
            
            model.epochs = 5
            
            training_results = model.train(X_test, y_test, verbose=False)
            
            results[arch['name']] = {
                'success': True,
                'actual_d_model': model.d_model,  # 可能被调整过
                'training_time': training_results['training_time'],
                'accuracy': training_results['training_accuracy']
            }
            
            print(f"   ✅ 成功")
            print(f"   实际d_model: {model.d_model}")
            print(f"   训练时间: {training_results['training_time']:.2f}秒")
            print(f"   准确率: {training_results['training_accuracy']:.4f}")
            
        except Exception as e:
            results[arch['name']] = {
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ 失败: {e}")
    
    return results

def test_learning_rate_robustness():
    """测试学习率鲁棒性"""
    print("\n⚡ 测试学习率鲁棒性")
    print("="*60)
    
    X_test = np.random.randn(500, 41).astype(np.float32)
    y_test = np.random.randint(0, 2, 500)
    
    learning_rates = [0.1, 0.01, 0.001, 0.0001, 0.00001]
    results = {}
    
    for lr in learning_rates:
        print(f"\n📊 测试学习率: {lr}")
        
        try:
            model = TransformerNIDS(
                input_size=41,
                d_model=64,
                nhead=8,
                num_layers=2,
                use_gpu=True,
                fast_mode=True
            )
            
            model.learning_rate = lr
            model.epochs = 5
            
            training_results = model.train(X_test, y_test, verbose=False)
            
            results[lr] = {
                'success': True,
                'training_time': training_results['training_time'],
                'accuracy': training_results['training_accuracy'],
                'epochs_completed': training_results['epochs_completed']
            }
            
            print(f"   ✅ 成功")
            print(f"   准确率: {training_results['training_accuracy']:.4f}")
            print(f"   完成轮数: {training_results['epochs_completed']}")
            
        except Exception as e:
            results[lr] = {
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ 失败: {e}")
    
    return results

def main():
    """主函数"""
    print("🔧 Transformer数值稳定性全面测试")
    print("="*60)
    
    if not torch.cuda.is_available():
        print("❌ GPU不可用，跳过测试")
        return
    
    all_results = {}
    
    # 测试1: 梯度稳定性
    print("🧪 第1轮测试: 梯度稳定性")
    all_results['gradient_stability'] = test_gradient_stability()
    
    # 测试2: 不同架构
    print("\n🧪 第2轮测试: 架构配置")
    all_results['architectures'] = test_different_architectures()
    
    # 测试3: 学习率鲁棒性
    print("\n🧪 第3轮测试: 学习率鲁棒性")
    all_results['learning_rates'] = test_learning_rate_robustness()
    
    # 总结
    print(f"\n📊 测试总结")
    print("="*60)
    
    total_tests = 0
    successful_tests = 0
    
    for category, results in all_results.items():
        print(f"\n{category.upper()}:")
        for test_name, result in results.items():
            total_tests += 1
            if result.get('success', False):
                successful_tests += 1
                print(f"  ✅ {test_name}")
            else:
                print(f"  ❌ {test_name}: {result.get('error', 'Unknown error')}")
    
    success_rate = successful_tests / total_tests * 100
    print(f"\n🎯 总体成功率: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 Transformer模型数值稳定性优秀！")
    elif success_rate >= 70:
        print("✅ Transformer模型数值稳定性良好")
    else:
        print("⚠️ Transformer模型数值稳定性需要改进")

if __name__ == "__main__":
    main()
