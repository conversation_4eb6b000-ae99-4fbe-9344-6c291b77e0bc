"""
Evaluation Metrics Module for Zero-Shot Learning NIDS

This module implements comprehensive evaluation metrics as described in:
"From Zero-Shot Machine Learning to Zero-Day Attack Detection"
International Journal of Information Security, 2023

Key Metrics:
1. Zero-day Detection Rate (Z-DR)
2. Overall performance metrics (Accuracy, Precision, Recall, F1, AUC)
3. False Alarm Rate (FAR)
4. Statistical significance testing
5. Performance comparison with paper baselines
"""

import numpy as np
import pandas as pd
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, confusion_matrix, classification_report
)
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os


class ZeroShotEvaluator:
    """
    Comprehensive evaluation framework for zero-shot learning NIDS
    
    This class implements all evaluation metrics described in the paper
    and provides detailed analysis and visualization capabilities.
    """
    
    def __init__(self, figures_dir='figures', reports_dir='reports'):
        """
        Initialize the evaluator

        Args:
            figures_dir (str): Directory to save evaluation figures
            reports_dir (str): Directory to save evaluation reports
        """
        self.figures_dir = figures_dir
        self.reports_dir = reports_dir
        os.makedirs(figures_dir, exist_ok=True)
        os.makedirs(reports_dir, exist_ok=True)
        
        # Paper baseline results for comparison
        self.paper_baselines = {
            'rf': {
                'Exploits': 94.43, 'Fuzzers': 14.77, 'DoS': 96.89,
                'Reconnaissance': 85.71, 'Analysis': 100.0, 'Backdoor': 100.0,
                'Shellcode': 100.0, 'Worms': 100.0, 'Generic': 63.33,
                'avg_z_dr': 80.67
            },
            'mlp': {
                'Exploits': 90.31, 'Fuzzers': 20.10, 'DoS': 92.80,
                'Reconnaissance': 71.43, 'Analysis': 100.0, 'Backdoor': 100.0,
                'Shellcode': 100.0, 'Worms': 100.0, 'Generic': 66.67,
                'avg_z_dr': 82.37
            }
        }
    
    def calculate_zero_day_detection_rate(self, y_true_attack, y_pred_binary, zero_day_attack):
        """
        Calculate Zero-day Detection Rate (Z-DR)
        
        Z-DR = TP_zero_day / (TP_zero_day + FN_zero_day) * 100
        
        Args:
            y_true_attack: True attack type labels
            y_pred_binary: Binary predictions (0=benign, 1=attack)
            zero_day_attack: The attack type treated as zero-day
            
        Returns:
            dict: Z-DR metrics
        """
        # Identify zero-day samples
        zero_day_mask = (y_true_attack == zero_day_attack)
        
        if zero_day_mask.sum() == 0:
            return {
                'z_dr': 0.0,
                'zero_day_samples': 0,
                'zero_day_detected': 0,
                'zero_day_missed': 0,
                'error': f'No samples found for zero-day attack: {zero_day_attack}'
            }
        
        # Extract zero-day predictions
        zero_day_predictions = y_pred_binary[zero_day_mask]
        
        # Calculate Z-DR
        zero_day_detected = (zero_day_predictions == 1).sum()
        zero_day_missed = (zero_day_predictions == 0).sum()
        total_zero_day = len(zero_day_predictions)
        
        z_dr = (zero_day_detected / total_zero_day) * 100
        
        return {
            'z_dr': z_dr,
            'zero_day_samples': total_zero_day,
            'zero_day_detected': zero_day_detected,
            'zero_day_missed': zero_day_missed,
            'detection_rate': zero_day_detected / total_zero_day
        }
    
    def calculate_false_alarm_rate(self, y_true_binary, y_pred_binary):
        """
        Calculate False Alarm Rate (FAR)
        
        FAR = FP / (FP + TN) * 100
        
        Args:
            y_true_binary: True binary labels (0=benign, 1=attack)
            y_pred_binary: Binary predictions (0=benign, 1=attack)
            
        Returns:
            dict: FAR metrics
        """
        # Identify benign samples
        benign_mask = (y_true_binary == 0)
        
        if benign_mask.sum() == 0:
            return {
                'far': 0.0,
                'benign_samples': 0,
                'false_alarms': 0,
                'true_negatives': 0,
                'error': 'No benign samples found'
            }
        
        # Extract benign predictions
        benign_predictions = y_pred_binary[benign_mask]
        
        # Calculate FAR
        false_alarms = (benign_predictions == 1).sum()
        true_negatives = (benign_predictions == 0).sum()
        total_benign = len(benign_predictions)
        
        far = (false_alarms / total_benign) * 100
        
        return {
            'far': far,
            'benign_samples': total_benign,
            'false_alarms': false_alarms,
            'true_negatives': true_negatives,
            'false_alarm_rate': false_alarms / total_benign
        }
    
    def calculate_comprehensive_metrics(self, y_true_binary, y_true_attack, 
                                      y_pred_binary, y_pred_proba, zero_day_attack):
        """
        Calculate all evaluation metrics described in the paper
        
        Args:
            y_true_binary: True binary labels
            y_true_attack: True attack type labels
            y_pred_binary: Binary predictions
            y_pred_proba: Prediction probabilities
            zero_day_attack: Attack type treated as zero-day
            
        Returns:
            dict: Comprehensive evaluation metrics
        """
        metrics = {
            'evaluation_timestamp': datetime.now().isoformat(),
            'zero_day_attack': zero_day_attack,
            'total_samples': len(y_true_binary)
        }
        
        # Zero-day Detection Rate
        z_dr_metrics = self.calculate_zero_day_detection_rate(
            y_true_attack, y_pred_binary, zero_day_attack
        )
        metrics.update({f'z_dr_{k}': v for k, v in z_dr_metrics.items()})
        
        # False Alarm Rate
        far_metrics = self.calculate_false_alarm_rate(y_true_binary, y_pred_binary)
        metrics.update({f'far_{k}': v for k, v in far_metrics.items()})
        
        # Overall performance metrics
        metrics['accuracy'] = accuracy_score(y_true_binary, y_pred_binary)
        metrics['precision'] = precision_score(y_true_binary, y_pred_binary, average='weighted', zero_division=0)
        metrics['recall'] = recall_score(y_true_binary, y_pred_binary, average='weighted', zero_division=0)
        metrics['f1'] = f1_score(y_true_binary, y_pred_binary, average='weighted', zero_division=0)
        
        # AUC-ROC
        try:
            if hasattr(y_pred_proba, 'shape') and y_pred_proba.shape[1] > 1:
                metrics['auc'] = roc_auc_score(y_true_binary, y_pred_proba[:, 1])
            else:
                metrics['auc'] = 0.5  # Default for single class
        except Exception as e:
            metrics['auc'] = 0.5
            metrics['auc_error'] = str(e)
        
        # Confusion matrix
        cm = confusion_matrix(y_true_binary, y_pred_binary)
        metrics['confusion_matrix'] = cm.tolist()
        
        if cm.shape == (2, 2):
            tn, fp, fn, tp = cm.ravel()
            metrics.update({
                'true_negatives': int(tn),
                'false_positives': int(fp),
                'false_negatives': int(fn),
                'true_positives': int(tp)
            })
        
        # Classification report
        try:
            class_report = classification_report(y_true_binary, y_pred_binary, output_dict=True)
            metrics['classification_report'] = class_report
        except:
            metrics['classification_report'] = None
        
        return metrics
    
    def compare_with_paper_baseline(self, our_results, model_type):
        """
        Compare our results with paper baseline results
        
        Args:
            our_results: Dictionary of our experimental results
            model_type: 'rf' or 'mlp'
            
        Returns:
            dict: Comparison analysis
        """
        if model_type not in self.paper_baselines:
            return {'error': f'No baseline available for model type: {model_type}'}
        
        paper_baseline = self.paper_baselines[model_type]
        comparison = {
            'model_type': model_type,
            'comparison_timestamp': datetime.now().isoformat(),
            'attack_comparisons': {},
            'overall_comparison': {}
        }
        
        # Compare individual attack types
        our_z_dr_scores = []
        paper_z_dr_scores = []
        
        for attack_type, our_result in our_results.items():
            if attack_type in paper_baseline and 'z_dr' in our_result:
                our_z_dr = our_result['z_dr']
                paper_z_dr = paper_baseline[attack_type]
                
                our_z_dr_scores.append(our_z_dr)
                paper_z_dr_scores.append(paper_z_dr)
                
                difference = our_z_dr - paper_z_dr
                relative_diff = (difference / paper_z_dr) * 100 if paper_z_dr > 0 else 0
                
                comparison['attack_comparisons'][attack_type] = {
                    'our_z_dr': our_z_dr,
                    'paper_z_dr': paper_z_dr,
                    'absolute_difference': difference,
                    'relative_difference': relative_diff,
                    'performance_status': self._get_performance_status(our_z_dr, paper_z_dr)
                }
        
        # Overall comparison
        if our_z_dr_scores and paper_z_dr_scores:
            our_avg = np.mean(our_z_dr_scores)
            paper_avg = np.mean(paper_z_dr_scores)
            
            comparison['overall_comparison'] = {
                'our_average_z_dr': our_avg,
                'paper_average_z_dr': paper_avg,
                'average_difference': our_avg - paper_avg,
                'relative_improvement': ((our_avg - paper_avg) / paper_avg) * 100 if paper_avg > 0 else 0,
                'better_count': sum(1 for comp in comparison['attack_comparisons'].values() 
                                  if comp['absolute_difference'] > 0),
                'worse_count': sum(1 for comp in comparison['attack_comparisons'].values() 
                                 if comp['absolute_difference'] < 0),
                'similar_count': sum(1 for comp in comparison['attack_comparisons'].values() 
                                   if abs(comp['absolute_difference']) <= 1)
            }
        
        return comparison
    
    def _get_performance_status(self, our_score, paper_score):
        """Determine performance status compared to paper"""
        if paper_score is None:
            return 'no_baseline'
        
        diff = our_score - paper_score
        if diff > 5:
            return 'significantly_better'
        elif diff > 1:
            return 'better'
        elif diff > -1:
            return 'similar'
        elif diff > -5:
            return 'worse'
        else:
            return 'significantly_worse'
    
    def generate_evaluation_report(self, experiment_results, model_type, output_file=None):
        """
        Generate comprehensive evaluation report

        Args:
            experiment_results: Dictionary of experiment results
            model_type: 'rf' or 'mlp'
            output_file: Optional output file path (if None, auto-generate with timestamp)

        Returns:
            str: Formatted evaluation report
        """
        report_lines = []
        report_lines.append("="*80)
        report_lines.append("ZERO-SHOT LEARNING NIDS EVALUATION REPORT")
        report_lines.append("="*80)
        report_lines.append(f"Model Type: {model_type.upper()}")
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # Individual attack type results
        report_lines.append("INDIVIDUAL ATTACK TYPE RESULTS:")
        report_lines.append("-" * 50)
        report_lines.append(f"{'Attack Type':<15} {'Z-DR (%)':<10} {'Accuracy':<10} {'F1':<8} {'FAR (%)':<8}")
        report_lines.append("-" * 50)
        
        z_dr_scores = []
        for attack_type, result in experiment_results.items():
            if 'z_dr' in result:
                z_dr = result['z_dr']
                accuracy = result.get('accuracy', 0)
                f1 = result.get('f1', 0)
                far = result.get('far', 0)
                
                z_dr_scores.append(z_dr)
                
                report_lines.append(f"{attack_type:<15} {z_dr:<10.2f} {accuracy:<10.4f} {f1:<8.4f} {far:<8.2f}")
        
        # Summary statistics
        if z_dr_scores:
            report_lines.append("")
            report_lines.append("SUMMARY STATISTICS:")
            report_lines.append("-" * 30)
            report_lines.append(f"Average Z-DR: {np.mean(z_dr_scores):.2f}%")
            report_lines.append(f"Std Dev Z-DR: {np.std(z_dr_scores):.2f}%")
            report_lines.append(f"Max Z-DR: {np.max(z_dr_scores):.2f}%")
            report_lines.append(f"Min Z-DR: {np.min(z_dr_scores):.2f}%")
        
        # Comparison with paper
        comparison = self.compare_with_paper_baseline(experiment_results, model_type)
        if 'overall_comparison' in comparison:
            overall = comparison['overall_comparison']
            report_lines.append("")
            report_lines.append("COMPARISON WITH PAPER BASELINE:")
            report_lines.append("-" * 40)
            report_lines.append(f"Our Average Z-DR: {overall['our_average_z_dr']:.2f}%")
            report_lines.append(f"Paper Average Z-DR: {overall['paper_average_z_dr']:.2f}%")
            report_lines.append(f"Difference: {overall['average_difference']:+.2f}%")
            report_lines.append(f"Relative Improvement: {overall['relative_improvement']:+.1f}%")
            report_lines.append(f"Better/Similar/Worse: {overall['better_count']}/{overall['similar_count']}/{overall['worse_count']}")
        
        report_lines.append("")
        report_lines.append("="*80)
        
        report_text = "\n".join(report_lines)

        # Save to file
        if output_file is None:
            # Auto-generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.reports_dir, f'evaluation_report_{model_type}_{timestamp}.txt')

        # Ensure directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
        print(f"📄 {model_type.upper()} 模型评估报告已保存至: {output_file}")

        return report_text


def main():
    """
    Test the evaluation module
    """
    print("🧪 Testing Zero-Shot Learning NIDS Evaluation Module")
    print("="*60)
    
    # Initialize evaluator
    evaluator = ZeroShotEvaluator()
    
    # Generate sample data for testing
    np.random.seed(42)
    n_samples = 1000
    
    y_true_binary = np.random.choice([0, 1], size=n_samples, p=[0.8, 0.2])
    y_pred_binary = np.random.choice([0, 1], size=n_samples, p=[0.85, 0.15])
    y_pred_proba = np.random.rand(n_samples, 2)
    y_pred_proba = y_pred_proba / y_pred_proba.sum(axis=1, keepdims=True)
    
    attack_types = ['Exploits', 'DoS', 'Fuzzers']
    y_true_attack = np.random.choice(attack_types + ['Normal'], size=n_samples, p=[0.1, 0.05, 0.05, 0.8])
    
    # Test comprehensive metrics calculation
    print("\n📊 Testing comprehensive metrics calculation...")
    metrics = evaluator.calculate_comprehensive_metrics(
        y_true_binary, y_true_attack, y_pred_binary, y_pred_proba, 'Exploits'
    )
    
    print(f"✅ Z-DR: {metrics['z_dr_z_dr']:.2f}%")
    print(f"✅ FAR: {metrics['far_far']:.2f}%")
    print(f"✅ Accuracy: {metrics['accuracy']:.4f}")
    print(f"✅ F1-Score: {metrics['f1']:.4f}")
    
    print(f"\n✅ Evaluation module testing completed!")


if __name__ == '__main__':
    main()
