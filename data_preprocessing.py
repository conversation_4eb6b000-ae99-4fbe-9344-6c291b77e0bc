"""
Data Preprocessing Module for Zero-Shot Learning NIDS

This module implements the data preprocessing pipeline described in:
"From Zero-Shot Machine Learning to Zero-Day Attack Detection"
International Journal of Information Security, 2023

Key preprocessing steps:
1. Remove flow identifiers (avoid learning bias)
2. Convert categorical features to numerical (label encoding)
3. Normalize all features to [0,1] using Min-Max scaling
4. Create zero-shot learning data splits
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
import warnings
warnings.filterwarnings('ignore')


class DataPreprocessor:
    """
    Data preprocessing class for UNSW-NB15 dataset
    
    Implements the preprocessing methodology from the paper for zero-shot learning
    in network intrusion detection systems.
    """
    
    def __init__(self):
        """Initialize the data preprocessor"""
        self.scaler = MinMaxScaler()
        self.label_encoders = {}
        self.feature_columns = None
        self.attack_types = None
        self.removed_columns = []
        self.is_fitted = False
    
    def load_data(self, train_path, test_path):
        """
        Load and combine training and testing data
        
        Args:
            train_path (str): Path to training data CSV
            test_path (str): Path to testing data CSV
            
        Returns:
            pd.DataFrame: Combined dataset
        """
        print("📂 Loading UNSW-NB15 dataset...")
        
        try:
            train_data = pd.read_csv(train_path)
            test_data = pd.read_csv(test_path)
            
            print(f"  Training set: {train_data.shape}")
            print(f"  Testing set: {test_data.shape}")
            
            # Combine datasets
            combined_data = pd.concat([train_data, test_data], ignore_index=True)
            
            print(f"  Combined dataset: {combined_data.shape}")
            print(f"  Total samples: {len(combined_data):,}")
            
            return combined_data
            
        except FileNotFoundError as e:
            print(f"❌ Error: Could not find data file - {e}")
            raise
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            raise
    
    def analyze_dataset(self, data):
        """
        Analyze the dataset and display comprehensive statistics
        
        Args:
            data (pd.DataFrame): Dataset to analyze
            
        Returns:
            dict: Analysis results
        """
        print("\n" + "="*60)
        print("DATASET ANALYSIS")
        print("="*60)
        
        # Basic information
        print(f"Dataset shape: {data.shape}")
        print(f"Total samples: {len(data):,}")
        print(f"Total features: {data.shape[1]}")
        
        # Check for missing values
        missing_values = data.isnull().sum()
        if missing_values.sum() > 0:
            print(f"\nMissing values found:")
            print(missing_values[missing_values > 0])
        else:
            print("\n✓ No missing values found")
        
        # Attack category distribution
        print("\nAttack Category Distribution:")
        attack_dist = data['attack_cat'].value_counts()
        for attack_type, count in attack_dist.items():
            percentage = (count / len(data)) * 100
            print(f"  {attack_type}: {count:,} ({percentage:.2f}%)")
        
        # Binary label distribution  
        print("\nBinary Label Distribution:")
        binary_dist = data['label'].value_counts()
        normal_pct = (binary_dist[0] / len(data)) * 100
        attack_pct = (binary_dist[1] / len(data)) * 100
        print(f"  Normal (0): {binary_dist[0]:,} ({normal_pct:.2f}%)")
        print(f"  Attack (1): {binary_dist[1]:,} ({attack_pct:.2f}%)")
        
        # Feature types analysis
        numerical_features = data.select_dtypes(include=[np.number]).columns
        categorical_features = data.select_dtypes(include=['object']).columns
        
        print(f"\nFeature Types:")
        print(f"  Numerical features: {len(numerical_features)}")
        print(f"  Categorical features: {len(categorical_features)}")
        
        if len(categorical_features) > 0:
            print(f"  Categorical columns: {list(categorical_features)}")
        
        # Data quality check
        print(f"\nData Quality:")
        print(f"  Duplicate rows: {data.duplicated().sum()}")
        print(f"  Memory usage: {data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        return {
            'total_samples': len(data),
            'total_features': data.shape[1],
            'attack_distribution': attack_dist,
            'binary_distribution': binary_dist,
            'missing_values': missing_values.sum(),
            'numerical_features': len(numerical_features),
            'categorical_features': len(categorical_features)
        }
    
    def remove_flow_identifiers(self, data):
        """
        Step 1: Remove flow identifiers to avoid learning bias
        
        Args:
            data (pd.DataFrame): Raw dataset
            
        Returns:
            pd.DataFrame: Dataset with identifiers removed
        """
        print(f"\n🗑️ REMOVING FLOW IDENTIFIERS")
        print("-" * 40)
        
        # Make a copy to avoid modifying original data
        data_processed = data.copy()
        
        # Check for common identifier columns
        identifier_patterns = ['id', 'srcip', 'sport', 'dstip', 'dsport', 'stime', 'ltime']
        
        columns_to_remove = []
        for pattern in identifier_patterns:
            matching_cols = [col for col in data_processed.columns 
                           if pattern.lower() in col.lower()]
            columns_to_remove.extend(matching_cols)
        
        # Remove duplicates
        columns_to_remove = list(set(columns_to_remove))
        
        # Only remove columns that actually exist
        existing_cols_to_remove = [col for col in columns_to_remove 
                                 if col in data_processed.columns]
        
        if existing_cols_to_remove:
            print(f"Removing columns: {existing_cols_to_remove}")
            data_processed = data_processed.drop(columns=existing_cols_to_remove)
            self.removed_columns = existing_cols_to_remove
        else:
            print("No flow identifiers found to remove")
            self.removed_columns = []
        
        print(f"Features after removal: {data_processed.shape[1]}")
        return data_processed
    
    def separate_features_labels(self, data):
        """
        Separate features and labels from the dataset
        
        Args:
            data (pd.DataFrame): Processed dataset
            
        Returns:
            tuple: (X, y_attack, y_binary)
        """
        print(f"\n📊 SEPARATING FEATURES AND LABELS")
        print("-" * 40)
        
        # Separate features and labels
        X = data.drop(['attack_cat', 'label'], axis=1)
        y_attack = data['attack_cat']  # Attack category
        y_binary = data['label']       # Binary label (0: Normal, 1: Attack)
        
        print(f"Features: {X.shape}")
        print(f"Attack categories: {len(y_attack.unique())} types")
        print(f"Binary labels: {y_binary.value_counts().to_dict()}")
        
        # Store information
        self.feature_columns = X.columns.tolist()
        self.attack_types = y_attack.unique()
        
        return X, y_attack, y_binary

    def encode_categorical_features(self, X, fit=True):
        """
        Step 2: Convert categorical features to numerical using label encoding

        Args:
            X (pd.DataFrame): Feature matrix
            fit (bool): Whether to fit encoders (True for training, False for test)

        Returns:
            pd.DataFrame: Encoded feature matrix
        """
        print(f"\n🔤 CATEGORICAL FEATURE ENCODING")
        print("-" * 40)

        X_encoded = X.copy()

        # Identify categorical columns
        categorical_columns = X_encoded.select_dtypes(include=['object']).columns

        if len(categorical_columns) == 0:
            print("No categorical features found")
            return X_encoded

        print(f"Found {len(categorical_columns)} categorical features:")

        for col in categorical_columns:
            unique_values = X_encoded[col].nunique()
            print(f"  - {col}: {unique_values} unique values")

            if fit:
                # Fit and transform (for training data)
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                X_encoded[col] = self.label_encoders[col].fit_transform(X_encoded[col].astype(str))
            else:
                # Transform only (for test data)
                if col in self.label_encoders:
                    # Handle unseen categories
                    try:
                        X_encoded[col] = self.label_encoders[col].transform(X_encoded[col].astype(str))
                    except ValueError as e:
                        print(f"    Warning: Unseen categories in {col}, using most frequent class")
                        # Replace unseen categories with most frequent class
                        known_classes = self.label_encoders[col].classes_
                        X_encoded[col] = X_encoded[col].astype(str).apply(
                            lambda x: x if x in known_classes else known_classes[0]
                        )
                        X_encoded[col] = self.label_encoders[col].transform(X_encoded[col])
                else:
                    print(f"    Warning: No encoder found for {col}")

            print(f"    ✓ Encoded to range [0, {X_encoded[col].max()}]")

        print(f"✅ Categorical encoding completed")
        return X_encoded

    def normalize_features(self, X, fit=True):
        """
        Step 3: Normalize all features to [0,1] using Min-Max scaling

        Args:
            X (pd.DataFrame): Feature matrix
            fit (bool): Whether to fit scaler (True for training, False for test)

        Returns:
            pd.DataFrame: Normalized feature matrix
        """
        print(f"\n📏 MIN-MAX NORMALIZATION")
        print("-" * 40)

        if fit:
            # Fit and transform (for training data)
            X_scaled = self.scaler.fit_transform(X)
        else:
            # Transform only (for test data)
            X_scaled = self.scaler.transform(X)

        X_scaled = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)

        # Verify normalization
        print(f"Feature ranges after normalization:")
        print(f"  - Minimum value: {X_scaled.min().min():.6f}")
        print(f"  - Maximum value: {X_scaled.max().max():.6f}")
        print(f"  - Mean: {X_scaled.mean().mean():.6f}")
        print(f"  - Std: {X_scaled.std().mean():.6f}")

        # Check if normalization was successful
        if X_scaled.min().min() >= 0 and X_scaled.max().max() <= 1:
            print("✅ Normalization successful - all features in [0,1] range")
        else:
            print("⚠️ Warning: Some features outside [0,1] range")

        return X_scaled

    def create_zero_shot_split(self, X, y_attack, y_binary, zero_day_attack):
        """
        Create zero-shot learning split by excluding one attack type as zero-day

        This implements the core ZSL methodology from the paper:
        - Training set: D_tr^z = {(x,y) | y ∈ {b, a1, a2, ..., an} \ {az}}
        - Test set: D_tst = {(x,y) | y ∈ {b, a1, a2, ..., an, az}}

        Args:
            X (pd.DataFrame): Preprocessed features
            y_attack (pd.Series): Attack categories
            y_binary (pd.Series): Binary labels
            zero_day_attack (str): Attack type to treat as zero-day (unseen)

        Returns:
            tuple: (X_train, y_train_attack, y_train_binary,
                   X_test, y_test_attack, y_test_binary, zero_day_mask)
        """
        print(f"\n🎯 ZERO-SHOT LEARNING DATA SPLIT")
        print("-" * 40)
        print(f"Zero-day attack (unseen class): {zero_day_attack}")

        # Create masks for known and unknown attacks
        known_mask = y_attack != zero_day_attack
        zero_day_mask = y_attack == zero_day_attack

        # Training set: Known attacks + Normal traffic (excluding zero-day)
        X_train_zsl = X[known_mask].copy()
        y_train_attack = y_attack[known_mask].copy()
        y_train_binary = y_binary[known_mask].copy()

        # Test set: All data (including zero-day attacks)
        X_test_zsl = X.copy()
        y_test_attack = y_attack.copy()
        y_test_binary = y_binary.copy()

        # Statistics
        total_samples = len(X)
        train_samples = len(X_train_zsl)
        test_samples = len(X_test_zsl)
        zero_day_samples = zero_day_mask.sum()

        print(f"📊 Data Split Statistics:")
        print(f"  Total samples: {total_samples:,}")
        print(f"  Training samples (known attacks + normal): {train_samples:,} ({train_samples/total_samples*100:.1f}%)")
        print(f"  Test samples (all data): {test_samples:,}")
        print(f"  Zero-day samples in test: {zero_day_samples:,} ({zero_day_samples/total_samples*100:.1f}%)")

        # Training set composition
        train_attack_dist = y_train_attack.value_counts()
        print(f"\n📈 Training Set Composition:")
        for attack_type, count in train_attack_dist.items():
            percentage = (count / len(y_train_attack)) * 100
            print(f"  {attack_type}: {count:,} ({percentage:.1f}%)")

        # Test set zero-day info
        if zero_day_samples > 0:
            print(f"\n🔍 Zero-day Attack Info:")
            print(f"  Type: {zero_day_attack}")
            print(f"  Samples: {zero_day_samples:,}")
            print(f"  Percentage of test set: {zero_day_samples/test_samples*100:.1f}%")
        else:
            print(f"⚠️ Warning: No samples found for zero-day attack '{zero_day_attack}'")

        return (X_train_zsl, y_train_attack, y_train_binary,
                X_test_zsl, y_test_attack, y_test_binary, zero_day_mask)

    def fit_transform(self, data, zero_day_attack=None, fast_mode=False, sample_size=10000):
        """
        Complete preprocessing pipeline - fit and transform

        Args:
            data (pd.DataFrame): Raw dataset
            zero_day_attack (str, optional): Attack type to treat as zero-day
            fast_mode (bool): Whether to use fast mode with reduced data
            sample_size (int): Number of samples to use in fast mode

        Returns:
            dict: Complete preprocessing results
        """
        print(f"\n🔄 COMPLETE PREPROCESSING PIPELINE (FIT & TRANSFORM)")
        if fast_mode:
            print(f"⚡ FAST MODE: Using {sample_size:,} samples for quick testing")
        print("="*60)

        # Fast mode: Sample data for quick testing
        if fast_mode and len(data) > sample_size:
            print(f"⚡ Sampling {sample_size:,} from {len(data):,} total samples...")
            # Stratified sampling to maintain class distribution
            data_sampled = data.groupby('attack_cat', group_keys=False).apply(
                lambda x: x.sample(min(len(x), sample_size // len(data['attack_cat'].unique())),
                                 random_state=42)
            ).reset_index(drop=True)
            print(f"✓ Sampled data: {len(data_sampled):,} samples")
            data = data_sampled

        # Step 1: Remove flow identifiers
        data_clean = self.remove_flow_identifiers(data)

        # Step 2: Separate features and labels
        X, y_attack, y_binary = self.separate_features_labels(data_clean)

        # Step 3: Categorical encoding (fit)
        X_encoded = self.encode_categorical_features(X, fit=True)

        # Step 4: Normalization (fit)
        X_normalized = self.normalize_features(X_encoded, fit=True)

        # Step 5: Zero-shot split (if specified)
        zsl_data = None
        if zero_day_attack:
            if zero_day_attack in y_attack.values:
                zsl_data = self.create_zero_shot_split(X_normalized, y_attack, y_binary, zero_day_attack)
            else:
                print(f"⚠️ Warning: '{zero_day_attack}' not found in attack types")
                print(f"Available types: {list(y_attack.unique())}")

        # Mark as fitted
        self.is_fitted = True

        # Summary
        print(f"\n✅ PREPROCESSING COMPLETED")
        print(f"📊 Final feature matrix: {X_normalized.shape}")
        print(f"🗑️ Removed columns: {self.removed_columns}")
        print(f"🔤 Encoded categorical features: {len(self.label_encoders)}")
        print(f"📏 Normalized to [0,1]: ✓")

        return {
            'X_processed': X_normalized,
            'y_attack': y_attack,
            'y_binary': y_binary,
            'removed_columns': self.removed_columns,
            'feature_columns': self.feature_columns,
            'attack_types': self.attack_types,
            'zsl_data': zsl_data
        }

    def transform(self, data):
        """
        Transform new data using fitted preprocessors

        Args:
            data (pd.DataFrame): Raw dataset to transform

        Returns:
            dict: Transformed data
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transform. Use fit_transform() first.")

        print(f"\n🔄 TRANSFORMING NEW DATA")
        print("="*40)

        # Step 1: Remove flow identifiers (same columns as training)
        data_clean = data.copy()
        if self.removed_columns:
            existing_cols_to_remove = [col for col in self.removed_columns
                                     if col in data_clean.columns]
            if existing_cols_to_remove:
                data_clean = data_clean.drop(columns=existing_cols_to_remove)
                print(f"Removed columns: {existing_cols_to_remove}")

        # Step 2: Separate features and labels
        X, y_attack, y_binary = self.separate_features_labels(data_clean)

        # Step 3: Categorical encoding (transform only)
        X_encoded = self.encode_categorical_features(X, fit=False)

        # Step 4: Normalization (transform only)
        X_normalized = self.normalize_features(X_encoded, fit=False)

        print(f"✅ Transform completed: {X_normalized.shape}")

        return {
            'X_processed': X_normalized,
            'y_attack': y_attack,
            'y_binary': y_binary
        }

    def save_preprocessor(self, filepath):
        """
        Save the fitted preprocessor to file

        Args:
            filepath (str): Path to save the preprocessor
        """
        import pickle

        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before saving")

        preprocessor_data = {
            'scaler': self.scaler,
            'label_encoders': self.label_encoders,
            'feature_columns': self.feature_columns,
            'attack_types': self.attack_types,
            'removed_columns': self.removed_columns,
            'is_fitted': self.is_fitted
        }

        with open(filepath, 'wb') as f:
            pickle.dump(preprocessor_data, f)

        print(f"✅ Preprocessor saved to {filepath}")

    def load_preprocessor(self, filepath):
        """
        Load a fitted preprocessor from file

        Args:
            filepath (str): Path to load the preprocessor from
        """
        import pickle

        with open(filepath, 'rb') as f:
            preprocessor_data = pickle.load(f)

        self.scaler = preprocessor_data['scaler']
        self.label_encoders = preprocessor_data['label_encoders']
        self.feature_columns = preprocessor_data['feature_columns']
        self.attack_types = preprocessor_data['attack_types']
        self.removed_columns = preprocessor_data['removed_columns']
        self.is_fitted = preprocessor_data['is_fitted']

        print(f"✅ Preprocessor loaded from {filepath}")


def get_attack_types():
    """
    Get the list of attack types in UNSW-NB15 dataset

    Returns:
        list: Attack type names
    """
    return ['Normal', 'Exploits', 'Fuzzers', 'DoS', 'Reconnaissance',
            'Analysis', 'Backdoor', 'Shellcode', 'Worms', 'Generic']


def main():
    """
    Test the data preprocessing module
    """
    print("🧪 Testing Data Preprocessing Module")
    print("="*50)

    # Initialize preprocessor
    preprocessor = DataPreprocessor()

    # Load data
    data = preprocessor.load_data(
        train_path='data/UNSW/UNSW_NB15_training-set.csv',
        test_path='data/UNSW/UNSW_NB15_testing-set.csv'
    )

    # Analyze dataset
    analysis = preprocessor.analyze_dataset(data)

    # Test preprocessing pipeline
    results = preprocessor.fit_transform(data, zero_day_attack='Exploits')

    print(f"\n🎉 Data preprocessing module test completed!")
    print(f"📊 Processed {results['X_processed'].shape[0]:,} samples")
    print(f"📊 {results['X_processed'].shape[1]} features")


if __name__ == '__main__':
    main()
