"""
Transformer嵌入使用示例

演示如何使用attack_descriptions_for_embedding.csv文件
为零样本学习创建攻击类型的语义嵌入
"""

import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
import seaborn as sns


def load_attack_descriptions(csv_path='attack_descriptions_for_embedding.csv'):
    """
    加载攻击描述CSV文件
    
    Returns:
        dict: 攻击类型到描述的映射
    """
    print("📁 加载攻击描述文件...")
    df = pd.read_csv(csv_path)
    
    attack_descriptions = {}
    for _, row in df.iterrows():
        attack_type = row['attack_type']
        
        # 组合所有描述字段创建完整描述
        full_description = (
            f"{row['short_description']} "
            f"{row['detailed_description']} "
            f"Behavioral patterns: {row['behavioral_patterns']} "
            f"Technical characteristics: {row['technical_characteristics']}"
        )
        
        attack_descriptions[attack_type] = {
            'short': row['short_description'],
            'detailed': row['detailed_description'],
            'behavioral': row['behavioral_patterns'],
            'technical': row['technical_characteristics'],
            'full': full_description
        }
    
    print(f"✅ 成功加载 {len(attack_descriptions)} 种攻击类型的描述")
    return attack_descriptions


def generate_embeddings(attack_descriptions, model_name='all-MiniLM-L6-v2'):
    """
    使用Sentence-BERT生成攻击描述的嵌入向量
    
    Args:
        attack_descriptions: 攻击描述字典
        model_name: 预训练模型名称
        
    Returns:
        dict: 攻击类型到嵌入向量的映射
    """
    print(f"🤖 使用 {model_name} 生成嵌入向量...")
    
    # 加载预训练模型
    model = SentenceTransformer(model_name)
    
    embeddings = {}
    for attack_type, descriptions in attack_descriptions.items():
        # 使用完整描述生成嵌入
        embedding = model.encode(descriptions['full'])
        embeddings[attack_type] = embedding
        print(f"  ✓ {attack_type}: {embedding.shape}")
    
    print("✅ 嵌入向量生成完成")
    return embeddings


def analyze_attack_similarities(embeddings):
    """
    分析攻击类型之间的语义相似度
    
    Args:
        embeddings: 攻击类型嵌入向量字典
    """
    print("\n📊 分析攻击类型语义相似度...")
    
    attack_types = list(embeddings.keys())
    embedding_matrix = np.array([embeddings[attack] for attack in attack_types])
    
    # 计算余弦相似度矩阵
    similarity_matrix = cosine_similarity(embedding_matrix)
    
    # 创建相似度DataFrame
    similarity_df = pd.DataFrame(
        similarity_matrix,
        index=attack_types,
        columns=attack_types
    )
    
    print("\n🔍 攻击类型相似度矩阵:")
    print(similarity_df.round(3))
    
    # 找到最相似的攻击对
    print("\n🎯 最相似的攻击类型对:")
    for i, attack1 in enumerate(attack_types):
        for j, attack2 in enumerate(attack_types):
            if i < j:  # 避免重复和自比较
                similarity = similarity_matrix[i][j]
                if similarity > 0.7:  # 高相似度阈值
                    print(f"  {attack1} ↔ {attack2}: {similarity:.3f}")
    
    return similarity_df


def visualize_similarities(similarity_df, save_path='attack_similarity_heatmap.png'):
    """
    可视化攻击类型相似度矩阵
    
    Args:
        similarity_df: 相似度DataFrame
        save_path: 保存路径
    """
    print(f"\n📈 生成相似度热力图...")
    
    plt.figure(figsize=(12, 10))
    sns.heatmap(similarity_df, 
                annot=True, 
                cmap='coolwarm', 
                center=0.5,
                square=True,
                fmt='.3f',
                cbar_kws={'label': 'Cosine Similarity'})
    
    plt.title('Attack Type Semantic Similarity Matrix', fontsize=16, pad=20)
    plt.xlabel('Attack Types', fontsize=12)
    plt.ylabel('Attack Types', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 热力图已保存到 {save_path}")
    plt.show()


def demonstrate_zero_shot_classification(embeddings, attack_descriptions):
    """
    演示如何使用嵌入进行零样本分类
    
    Args:
        embeddings: 攻击类型嵌入向量
        attack_descriptions: 攻击描述字典
    """
    print("\n🎯 零样本分类演示...")
    
    # 模拟一个新的攻击描述（未知攻击类型）
    unknown_attack_description = (
        "A sophisticated attack that combines multiple exploitation techniques "
        "to gain persistent access to target systems. The attack uses advanced "
        "evasion techniques and establishes covert communication channels."
    )
    
    print(f"🔍 未知攻击描述: {unknown_attack_description}")
    
    # 生成未知攻击的嵌入
    model = SentenceTransformer('all-MiniLM-L6-v2')
    unknown_embedding = model.encode(unknown_attack_description)
    
    # 计算与已知攻击类型的相似度
    similarities = {}
    for attack_type, known_embedding in embeddings.items():
        similarity = cosine_similarity([unknown_embedding], [known_embedding])[0][0]
        similarities[attack_type] = similarity
    
    # 排序并显示最相似的攻击类型
    sorted_similarities = sorted(similarities.items(), key=lambda x: x[1], reverse=True)
    
    print("\n📋 零样本分类结果 (按相似度排序):")
    for i, (attack_type, similarity) in enumerate(sorted_similarities[:5]):
        print(f"  {i+1}. {attack_type}: {similarity:.3f}")
        if i == 0:
            print(f"     → 最可能的攻击类型: {attack_type}")


def save_embeddings_for_model(embeddings, save_path='attack_embeddings.npz'):
    """
    保存嵌入向量供模型使用
    
    Args:
        embeddings: 嵌入向量字典
        save_path: 保存路径
    """
    print(f"\n💾 保存嵌入向量到 {save_path}...")
    
    attack_types = list(embeddings.keys())
    embedding_matrix = np.array([embeddings[attack] for attack in attack_types])
    
    np.savez(save_path,
             embeddings=embedding_matrix,
             attack_types=attack_types)
    
    print(f"✅ 嵌入向量已保存")
    print(f"   - 攻击类型数量: {len(attack_types)}")
    print(f"   - 嵌入维度: {embedding_matrix.shape[1]}")


def main():
    """主函数：完整的嵌入生成和分析流程"""
    print("🚀 Transformer攻击描述嵌入演示")
    print("="*60)
    
    # 1. 加载攻击描述
    attack_descriptions = load_attack_descriptions()
    
    # 2. 生成嵌入向量
    embeddings = generate_embeddings(attack_descriptions)
    
    # 3. 分析相似度
    similarity_df = analyze_attack_similarities(embeddings)
    
    # 4. 可视化
    visualize_similarities(similarity_df)
    
    # 5. 零样本分类演示
    demonstrate_zero_shot_classification(embeddings, attack_descriptions)
    
    # 6. 保存嵌入向量
    save_embeddings_for_model(embeddings)
    
    print("\n🎉 演示完成！")
    print("\n💡 使用建议:")
    print("1. 将生成的嵌入向量集成到您的Transformer模型中")
    print("2. 使用攻击类型嵌入作为零样本学习的语义先验")
    print("3. 根据相似度分析调整模型的分类策略")


if __name__ == "__main__":
    main()
