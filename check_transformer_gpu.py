#!/usr/bin/env python3
"""
GPU使用检测脚本 - 专门检测Transformer模型的GPU使用情况
"""

import torch
import time
import psutil
import numpy as np
from models import TransformerNIDS

def check_gpu_status():
    """检查GPU状态"""
    print("🔍 GPU状态检查")
    print("="*60)
    
    if torch.cuda.is_available():
        print(f"✅ CUDA可用")
        print(f"   GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
            props = torch.cuda.get_device_properties(i)
            print(f"           显存: {props.total_memory / 1e9:.1f} GB")
        
        # 显存使用情况
        allocated = torch.cuda.memory_allocated() / 1e9
        reserved = torch.cuda.memory_reserved() / 1e9
        print(f"\n💾 当前显存使用:")
        print(f"   已分配: {allocated:.2f} GB")
        print(f"   已保留: {reserved:.2f} GB")
        return True
    else:
        print("❌ CUDA不可用")
        return False

def monitor_gpu_usage():
    """监控GPU使用情况"""
    if not torch.cuda.is_available():
        return None
    
    # 获取GPU使用率
    try:
        import pynvml
        pynvml.nvmlInit()
        handle = pynvml.nvmlDeviceGetHandleByIndex(0)
        info = pynvml.nvmlDeviceGetUtilizationRates(handle)
        memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
        
        return {
            'gpu_util': info.gpu,
            'memory_util': info.memory,
            'memory_used': memory_info.used / 1e9,
            'memory_total': memory_info.total / 1e9
        }
    except:
        # 如果pynvml不可用，使用torch的内存信息
        return {
            'memory_used': torch.cuda.memory_allocated() / 1e9,
            'memory_reserved': torch.cuda.memory_reserved() / 1e9
        }

def test_transformer_gpu_usage():
    """测试Transformer模型的GPU使用情况"""
    print("\n🧪 测试Transformer模型GPU使用")
    print("="*60)
    
    # 创建测试数据
    print("📊 创建测试数据...")
    X_test = np.random.randn(1000, 41).astype(np.float32)
    y_test = np.random.randint(0, 2, 1000)
    
    print(f"   测试数据: {X_test.shape}")
    print(f"   标签数据: {y_test.shape}")
    
    # 测试GPU模型
    print("\n🔥 测试GPU模式...")
    gpu_model = TransformerNIDS(
        input_size=41, 
        d_model=128, 
        nhead=8, 
        num_layers=4, 
        use_gpu=True, 
        fast_mode=True  # 使用快速模式减少训练时间
    )
    
    print(f"   模型设备: {gpu_model.device}")
    print(f"   GPU可用: {gpu_model.gpu_available}")
    
    # 记录训练前的GPU状态
    print("\n📈 训练前GPU状态:")
    gpu_before = monitor_gpu_usage()
    if gpu_before:
        for key, value in gpu_before.items():
            print(f"   {key}: {value}")
    
    # 训练模型并监控
    print("\n🚀 开始训练...")
    start_time = time.time()
    
    # 设置更少的训练轮数以快速测试
    gpu_model.epochs = 5
    
    results = gpu_model.train(X_test, y_test, verbose=True)
    
    training_time = time.time() - start_time
    
    # 记录训练后的GPU状态
    print("\n📈 训练后GPU状态:")
    gpu_after = monitor_gpu_usage()
    if gpu_after:
        for key, value in gpu_after.items():
            print(f"   {key}: {value}")
    
    print(f"\n⏱️ 训练时间: {training_time:.2f}秒")
    print(f"📊 训练结果: {results}")
    
    # 测试推理速度
    print("\n🔍 测试推理速度...")
    inference_start = time.time()
    predictions = gpu_model.predict(X_test[:100])
    inference_time = time.time() - inference_start
    
    print(f"   推理时间 (100样本): {inference_time:.4f}秒")
    print(f"   平均每样本: {inference_time/100*1000:.2f}ms")
    
    return {
        'training_time': training_time,
        'inference_time': inference_time,
        'device': str(gpu_model.device),
        'gpu_available': gpu_model.gpu_available,
        'model_parameters': sum(p.numel() for p in gpu_model.model.parameters()) if gpu_model.model else 0
    }

def compare_cpu_gpu_performance():
    """比较CPU和GPU性能"""
    print("\n⚖️ CPU vs GPU 性能对比")
    print("="*60)
    
    # 创建相同的测试数据
    X_test = np.random.randn(500, 41).astype(np.float32)
    y_test = np.random.randint(0, 2, 500)
    
    results = {}
    
    # 测试CPU模式
    print("🖥️ 测试CPU模式...")
    cpu_model = TransformerNIDS(
        input_size=41, 
        d_model=64,  # 减小模型以加快测试
        nhead=4, 
        num_layers=2, 
        use_gpu=False, 
        fast_mode=True
    )
    cpu_model.epochs = 3
    
    cpu_start = time.time()
    cpu_model.train(X_test, y_test, verbose=False)
    cpu_time = time.time() - cpu_start
    results['cpu_time'] = cpu_time
    
    print(f"   CPU训练时间: {cpu_time:.2f}秒")
    
    # 测试GPU模式（如果可用）
    if torch.cuda.is_available():
        print("\n🔥 测试GPU模式...")
        gpu_model = TransformerNIDS(
            input_size=41, 
            d_model=64, 
            nhead=4, 
            num_layers=2, 
            use_gpu=True, 
            fast_mode=True
        )
        gpu_model.epochs = 3
        
        gpu_start = time.time()
        gpu_model.train(X_test, y_test, verbose=False)
        gpu_time = time.time() - gpu_start
        results['gpu_time'] = gpu_time
        
        print(f"   GPU训练时间: {gpu_time:.2f}秒")
        
        # 计算加速比
        speedup = cpu_time / gpu_time if gpu_time > 0 else 0
        print(f"\n📊 性能对比:")
        print(f"   CPU时间: {cpu_time:.2f}秒")
        print(f"   GPU时间: {gpu_time:.2f}秒")
        print(f"   加速比: {speedup:.2f}x")
        
        if speedup < 1.1:
            print("⚠️ 警告: GPU加速效果不明显，可能原因:")
            print("   - 数据量太小，GPU优势无法体现")
            print("   - 模型太小，计算量不足")
            print("   - 数据传输开销大于计算收益")
        
        results['speedup'] = speedup
    
    return results

def main():
    """主函数"""
    print("🔬 Transformer GPU使用检测工具")
    print("="*60)
    
    # 1. 检查GPU状态
    gpu_available = check_gpu_status()
    
    if not gpu_available:
        print("\n❌ GPU不可用，无法进行GPU测试")
        return
    
    # 2. 测试Transformer GPU使用
    try:
        transformer_results = test_transformer_gpu_usage()
        print(f"\n✅ Transformer GPU测试完成")
        print(f"   设备: {transformer_results['device']}")
        print(f"   参数数量: {transformer_results['model_parameters']:,}")
    except Exception as e:
        print(f"\n❌ Transformer GPU测试失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 3. 性能对比
    try:
        perf_results = compare_cpu_gpu_performance()
        print(f"\n✅ 性能对比完成")
    except Exception as e:
        print(f"\n❌ 性能对比失败: {e}")
    
    # 4. 总结和建议
    print("\n📋 总结和建议")
    print("="*60)
    
    if transformer_results['gpu_available']:
        print("✅ Transformer模型成功使用GPU")
        print("💡 建议:")
        print("   - 使用更大的数据集以充分利用GPU优势")
        print("   - 增加模型复杂度（更多层数、更大维度）")
        print("   - 使用更大的批次大小")
    else:
        print("❌ Transformer模型未能使用GPU")
        print("🔧 排查建议:")
        print("   - 检查CUDA安装")
        print("   - 检查PyTorch CUDA版本")
        print("   - 检查GPU驱动")

if __name__ == "__main__":
    main()
