#!/bin/bash
# GPU训练启动脚本

echo "🚀 启动完整数据集GPU训练"

# 检查数据文件
if [ ! -f "data/UNSW/UNSW_NB15_training-set.csv" ]; then
    echo "❌ 训练集文件不存在: data/UNSW/UNSW_NB15_training-set.csv"
    exit 1
fi

if [ ! -f "data/UNSW/UNSW_NB15_testing-set.csv" ]; then
    echo "❌ 测试集文件不存在: data/UNSW/UNSW_NB15_testing-set.csv"
    exit 1
fi

# 运行完整训练
echo "开始MLP模型完整训练..."
python full_training.py --models mlp --epochs 50 --batch-size 2048

echo "开始RF模型完整训练..."
python full_training.py --models rf

echo "开始MLP+RF对比训练..."
python full_training.py --models mlp,rf --epochs 30 --batch-size 1024

echo "✅ 完整训练完成!"
