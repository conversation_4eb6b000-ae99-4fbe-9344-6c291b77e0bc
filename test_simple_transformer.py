#!/usr/bin/env python3
"""
简单的Transformer测试脚本
"""

import numpy as np
import torch
from models import TransformerNIDS

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试Transformer基本功能")
    print("="*50)
    
    # 创建测试数据
    X_test = np.random.randn(200, 41).astype(np.float32)
    y_test = np.random.randint(0, 2, 200)
    
    print(f"📊 测试数据: {X_test.shape}")
    print(f"📊 标签分布: {np.bincount(y_test)}")
    
    # 创建模型
    print("\n🔧 创建模型...")
    model = TransformerNIDS(
        input_size=41,
        d_model=64,
        nhead=8,
        num_layers=2,
        use_gpu=True,
        fast_mode=True
    )
    
    print(f"   模型设备: {model.device}")
    print(f"   d_model: {model.d_model}")
    print(f"   nhead: {model.nhead}")
    
    # 训练模型
    print("\n🚀 开始训练...")
    model.epochs = 5
    
    try:
        results = model.train(X_test, y_test, verbose=True)
        
        print(f"\n✅ 训练成功!")
        print(f"   训练时间: {results['training_time']:.2f}秒")
        print(f"   最终准确率: {results['training_accuracy']:.4f}")
        print(f"   完成轮数: {results['epochs_completed']}")
        
        # 测试预测
        print("\n🔍 测试预测...")
        predictions = model.predict(X_test[:50])
        probabilities = model.predict_proba(X_test[:50])
        
        print(f"   预测形状: {predictions.shape}")
        print(f"   概率形状: {probabilities.shape}")
        print(f"   预测中NaN数量: {np.isnan(predictions).sum()}")
        print(f"   概率中NaN数量: {np.isnan(probabilities).sum()}")
        
        if np.isnan(predictions).sum() == 0 and np.isnan(probabilities).sum() == 0:
            print("🎉 无NaN值，测试通过!")
            return True
        else:
            print("❌ 仍有NaN值")
            return False
            
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_extreme_data():
    """测试极端数据"""
    print("\n🔬 测试极端数据处理")
    print("="*50)
    
    # 创建包含极值的数据
    X_extreme = np.random.randn(100, 41).astype(np.float32)
    # 添加极值
    X_extreme[0, :] = 1e6   # 很大的值
    X_extreme[1, :] = -1e6  # 很小的值
    X_extreme[2, :] = 0     # 全零
    
    y_extreme = np.random.randint(0, 2, 100)
    
    print(f"📊 极值数据: {X_extreme.shape}")
    print(f"   最大值: {X_extreme.max()}")
    print(f"   最小值: {X_extreme.min()}")
    
    model = TransformerNIDS(
        input_size=41,
        d_model=64,
        nhead=8,
        num_layers=2,
        use_gpu=True,
        fast_mode=True
    )
    
    model.epochs = 3
    
    try:
        results = model.train(X_extreme, y_extreme, verbose=False)
        predictions = model.predict(X_extreme[:20])
        probabilities = model.predict_proba(X_extreme[:20])
        
        print(f"✅ 极值测试通过")
        print(f"   训练准确率: {results['training_accuracy']:.4f}")
        print(f"   预测NaN数量: {np.isnan(predictions).sum()}")
        print(f"   概率NaN数量: {np.isnan(probabilities).sum()}")
        
        return np.isnan(predictions).sum() == 0 and np.isnan(probabilities).sum() == 0
        
    except Exception as e:
        print(f"❌ 极值测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Transformer简单测试")
    print("="*50)
    
    if not torch.cuda.is_available():
        print("❌ GPU不可用")
        return
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 基本功能
    if test_basic_functionality():
        success_count += 1
    
    # 测试2: 极端数据
    if test_extreme_data():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！梯度NaN问题已解决")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
