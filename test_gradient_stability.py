#!/usr/bin/env python3
"""
测试梯度稳定性的专门脚本
"""

import numpy as np
import torch
from models import TransformerNIDS

def test_extreme_conditions():
    """测试极端条件下的梯度稳定性"""
    print("🔬 测试极端条件下的梯度稳定性")
    print("="*60)
    
    test_cases = [
        {
            'name': '极大值数据',
            'X': np.full((200, 41), 1e6, dtype=np.float32),
            'y': np.random.randint(0, 2, 200),
            'description': '所有特征值都是1e6'
        },
        {
            'name': '极小值数据', 
            'X': np.full((200, 41), -1e6, dtype=np.float32),
            'y': np.random.randint(0, 2, 200),
            'description': '所有特征值都是-1e6'
        },
        {
            'name': '混合极值数据',
            'X': np.concatenate([
                np.full((100, 41), 1e6, dtype=np.float32),
                np.full((100, 41), -1e6, dtype=np.float32)
            ]),
            'y': np.random.randint(0, 2, 200),
            'description': '一半极大值，一半极小值'
        },
        {
            'name': '包含NaN数据',
            'X': np.random.randn(200, 41).astype(np.float32),
            'y': np.random.randint(0, 2, 200),
            'description': '包含NaN值的数据'
        },
        {
            'name': '包含Inf数据',
            'X': np.random.randn(200, 41).astype(np.float32),
            'y': np.random.randint(0, 2, 200),
            'description': '包含无穷值的数据'
        }
    ]
    
    # 为NaN和Inf测试添加特殊值
    test_cases[3]['X'][0:10, :] = np.nan
    test_cases[4]['X'][0:10, :] = np.inf
    test_cases[4]['X'][10:20, :] = -np.inf
    
    results = {}
    
    for test_case in test_cases:
        print(f"\n📊 测试: {test_case['name']}")
        print(f"   描述: {test_case['description']}")
        print("-" * 50)
        
        # 创建模型
        model = TransformerNIDS(
            input_size=41,
            d_model=64,
            nhead=8,
            num_layers=2,
            use_gpu=True,
            fast_mode=True
        )
        
        model.epochs = 5
        
        try:
            print(f"   数据统计:")
            print(f"     形状: {test_case['X'].shape}")
            print(f"     最大值: {np.nanmax(test_case['X'])}")
            print(f"     最小值: {np.nanmin(test_case['X'])}")
            print(f"     NaN数量: {np.isnan(test_case['X']).sum()}")
            print(f"     Inf数量: {np.isinf(test_case['X']).sum()}")
            
            # 训练模型
            training_results = model.train(test_case['X'], test_case['y'], verbose=False)
            
            # 测试预测
            predictions = model.predict(test_case['X'][:50])
            probabilities = model.predict_proba(test_case['X'][:50])
            
            # 检查结果
            results[test_case['name']] = {
                'success': True,
                'training_time': training_results['training_time'],
                'final_accuracy': training_results['training_accuracy'],
                'epochs_completed': training_results['epochs_completed'],
                'nan_predictions': np.isnan(predictions).sum(),
                'nan_probabilities': np.isnan(probabilities).sum(),
                'inf_predictions': np.isinf(predictions).sum(),
                'inf_probabilities': np.isinf(probabilities).sum()
            }
            
            print(f"   ✅ 训练成功!")
            print(f"     训练时间: {training_results['training_time']:.2f}秒")
            print(f"     最终准确率: {training_results['training_accuracy']:.4f}")
            print(f"     完成轮数: {training_results['epochs_completed']}")
            print(f"     预测NaN数: {results[test_case['name']]['nan_predictions']}")
            print(f"     预测Inf数: {results[test_case['name']]['inf_predictions']}")
            print(f"     概率NaN数: {results[test_case['name']]['nan_probabilities']}")
            print(f"     概率Inf数: {results[test_case['name']]['inf_probabilities']}")
            
        except Exception as e:
            results[test_case['name']] = {
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ 训练失败: {e}")
    
    return results

def test_high_learning_rates():
    """测试高学习率下的稳定性"""
    print("\n⚡ 测试高学习率下的稳定性")
    print("="*60)
    
    X_test = np.random.randn(300, 41).astype(np.float32)
    y_test = np.random.randint(0, 2, 300)
    
    learning_rates = [0.1, 0.5, 1.0, 2.0, 5.0]  # 故意使用很高的学习率
    results = {}
    
    for lr in learning_rates:
        print(f"\n📊 测试学习率: {lr}")
        
        try:
            model = TransformerNIDS(
                input_size=41,
                d_model=64,
                nhead=8,
                num_layers=2,
                use_gpu=True,
                fast_mode=True
            )
            
            # 强制设置高学习率（绕过内部限制）
            model.learning_rate = lr
            model.epochs = 3
            
            training_results = model.train(X_test, y_test, verbose=False)
            
            results[lr] = {
                'success': True,
                'training_time': training_results['training_time'],
                'accuracy': training_results['training_accuracy'],
                'epochs_completed': training_results['epochs_completed']
            }
            
            print(f"   ✅ 成功 (实际学习率可能被限制)")
            print(f"     准确率: {training_results['training_accuracy']:.4f}")
            print(f"     完成轮数: {training_results['epochs_completed']}")
            
        except Exception as e:
            results[lr] = {
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ 失败: {e}")
    
    return results

def test_gradient_monitoring():
    """测试梯度监控功能"""
    print("\n🔍 测试梯度监控功能")
    print("="*60)
    
    # 创建一个会导致梯度问题的数据集
    X_problematic = np.random.randn(100, 41).astype(np.float32)
    X_problematic[0:20, :] = 1e8  # 极大值
    X_problematic[20:40, :] = -1e8  # 极小值
    X_problematic[40:60, :] = np.nan  # NaN值
    X_problematic[60:80, :] = np.inf  # 无穷值
    
    y_problematic = np.random.randint(0, 2, 100)
    
    print("📊 使用故意设计的问题数据:")
    print(f"   极大值样本: 20个")
    print(f"   极小值样本: 20个") 
    print(f"   NaN样本: 20个")
    print(f"   Inf样本: 20个")
    print(f"   正常样本: 20个")
    
    model = TransformerNIDS(
        input_size=41,
        d_model=64,
        nhead=8,
        num_layers=2,
        use_gpu=True,
        fast_mode=True
    )
    
    model.epochs = 5
    
    try:
        training_results = model.train(X_problematic, y_problematic, verbose=True)
        
        print(f"\n✅ 问题数据训练成功!")
        print(f"   训练时间: {training_results['training_time']:.2f}秒")
        print(f"   最终准确率: {training_results['training_accuracy']:.4f}")
        print(f"   完成轮数: {training_results['epochs_completed']}")
        
        # 测试预测
        predictions = model.predict(X_problematic[:30])
        probabilities = model.predict_proba(X_problematic[:30])
        
        print(f"   预测结果:")
        print(f"     NaN预测数: {np.isnan(predictions).sum()}")
        print(f"     Inf预测数: {np.isinf(predictions).sum()}")
        print(f"     NaN概率数: {np.isnan(probabilities).sum()}")
        print(f"     Inf概率数: {np.isinf(probabilities).sum()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 问题数据训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛡️ Transformer梯度稳定性终极测试")
    print("="*60)
    
    if not torch.cuda.is_available():
        print("❌ GPU不可用，跳过测试")
        return
    
    all_results = {}
    
    # 测试1: 极端条件
    print("🧪 第1轮: 极端条件测试")
    all_results['extreme_conditions'] = test_extreme_conditions()
    
    # 测试2: 高学习率
    print("\n🧪 第2轮: 高学习率测试")
    all_results['high_learning_rates'] = test_high_learning_rates()
    
    # 测试3: 梯度监控
    print("\n🧪 第3轮: 梯度监控测试")
    gradient_monitoring_success = test_gradient_monitoring()
    
    # 总结
    print(f"\n📊 最终测试总结")
    print("="*60)
    
    total_tests = 0
    successful_tests = 0
    
    for category, results in all_results.items():
        print(f"\n{category.upper()}:")
        for test_name, result in results.items():
            total_tests += 1
            if result.get('success', False):
                successful_tests += 1
                print(f"  ✅ {test_name}")
                
                # 检查是否有数值问题
                nan_issues = result.get('nan_predictions', 0) + result.get('nan_probabilities', 0)
                inf_issues = result.get('inf_predictions', 0) + result.get('inf_probabilities', 0)
                
                if nan_issues > 0 or inf_issues > 0:
                    print(f"    ⚠️ 仍有数值问题: NaN={nan_issues}, Inf={inf_issues}")
            else:
                print(f"  ❌ {test_name}: {result.get('error', 'Unknown error')}")
    
    if gradient_monitoring_success:
        successful_tests += 1
        print(f"  ✅ 梯度监控测试")
    else:
        print(f"  ❌ 梯度监控测试")
    total_tests += 1
    
    success_rate = successful_tests / total_tests * 100
    print(f"\n🎯 总体成功率: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("🎉 完美！所有梯度稳定性测试通过！")
        print("💪 Transformer模型现在具有工业级数值稳定性")
    elif success_rate >= 90:
        print("✅ 优秀！梯度稳定性大幅改善")
    elif success_rate >= 70:
        print("👍 良好！梯度稳定性显著提升")
    else:
        print("⚠️ 仍需改进梯度稳定性")

if __name__ == "__main__":
    main()
