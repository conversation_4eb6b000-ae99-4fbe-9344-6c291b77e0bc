"""
Attack Description Embedding Module for Zero-Shot Learning

This module provides functionality to create semantic embeddings from attack descriptions
for use in Transformer-based zero-shot learning models.

Features:
1. Load attack descriptions from CSV
2. Generate embeddings using pre-trained language models
3. Create attack type embeddings for zero-shot classification
4. Support for multiple embedding models (BERT, RoBERTa, etc.)
"""

import pandas as pd
import numpy as np
import torch
from transformers import AutoTokenizer, AutoModel
from sklearn.metrics.pairwise import cosine_similarity
import logging
from typing import Dict, List, Tuple, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AttackDescriptionEmbedder:
    """
    攻击描述嵌入器，用于生成攻击类型的语义嵌入
    """
    
    def __init__(self, model_name: str = 'sentence-transformers/all-MiniLM-L6-v2'):
        """
        初始化嵌入器
        
        Args:
            model_name: 预训练模型名称
        """
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.attack_embeddings = {}
        self.attack_descriptions = {}
        
        logger.info(f"初始化攻击描述嵌入器，使用模型: {model_name}")
        
    def load_model(self):
        """加载预训练的Transformer模型"""
        try:
            logger.info("加载预训练模型...")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModel.from_pretrained(self.model_name)
            self.model.eval()
            logger.info("模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def load_attack_descriptions(self, csv_path: str = 'attack_descriptions_for_embedding.csv'):
        """
        从CSV文件加载攻击描述
        
        Args:
            csv_path: CSV文件路径
        """
        try:
            logger.info(f"从 {csv_path} 加载攻击描述...")
            df = pd.read_csv(csv_path)
            
            for _, row in df.iterrows():
                attack_type = row['attack_type']
                
                # 组合多个描述字段创建完整的攻击描述
                full_description = f"{row['short_description']} {row['detailed_description']} {row['behavioral_patterns']} {row['technical_characteristics']}"
                
                self.attack_descriptions[attack_type] = {
                    'short_description': row['short_description'],
                    'detailed_description': row['detailed_description'],
                    'behavioral_patterns': row['behavioral_patterns'],
                    'technical_characteristics': row['technical_characteristics'],
                    'full_description': full_description
                }
            
            logger.info(f"成功加载 {len(self.attack_descriptions)} 种攻击类型的描述")
            return self.attack_descriptions
            
        except Exception as e:
            logger.error(f"加载攻击描述失败: {e}")
            raise
    
    def generate_embeddings(self, use_full_description: bool = True):
        """
        为所有攻击类型生成嵌入向量
        
        Args:
            use_full_description: 是否使用完整描述（包含所有字段）
        """
        if not self.model or not self.tokenizer:
            self.load_model()
        
        if not self.attack_descriptions:
            self.load_attack_descriptions()
        
        logger.info("生成攻击类型嵌入向量...")
        
        for attack_type, descriptions in self.attack_descriptions.items():
            # 选择使用的描述文本
            if use_full_description:
                text = descriptions['full_description']
            else:
                text = descriptions['detailed_description']
            
            # 生成嵌入
            embedding = self._encode_text(text)
            self.attack_embeddings[attack_type] = embedding
            
            logger.info(f"生成 {attack_type} 嵌入向量，维度: {embedding.shape}")
        
        logger.info(f"完成所有 {len(self.attack_embeddings)} 种攻击类型的嵌入生成")
        return self.attack_embeddings
    
    def _encode_text(self, text: str) -> np.ndarray:
        """
        将文本编码为嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            嵌入向量
        """
        # 分词和编码
        inputs = self.tokenizer(text, return_tensors='pt', truncation=True, 
                               padding=True, max_length=512)
        
        # 生成嵌入
        with torch.no_grad():
            outputs = self.model(**inputs)
            # 使用[CLS] token的嵌入或平均池化
            embeddings = outputs.last_hidden_state.mean(dim=1)
        
        return embeddings.numpy().flatten()
    
    def get_attack_similarity_matrix(self) -> pd.DataFrame:
        """
        计算攻击类型之间的相似度矩阵
        
        Returns:
            相似度矩阵DataFrame
        """
        if not self.attack_embeddings:
            self.generate_embeddings()
        
        attack_types = list(self.attack_embeddings.keys())
        embeddings = [self.attack_embeddings[attack] for attack in attack_types]
        
        # 计算余弦相似度
        similarity_matrix = cosine_similarity(embeddings)
        
        # 创建DataFrame
        similarity_df = pd.DataFrame(
            similarity_matrix,
            index=attack_types,
            columns=attack_types
        )
        
        return similarity_df
    
    def find_most_similar_attacks(self, target_attack: str, top_k: int = 3) -> List[Tuple[str, float]]:
        """
        找到与目标攻击最相似的攻击类型
        
        Args:
            target_attack: 目标攻击类型
            top_k: 返回最相似的前k个
            
        Returns:
            相似攻击类型和相似度分数的列表
        """
        similarity_matrix = self.get_attack_similarity_matrix()
        
        if target_attack not in similarity_matrix.index:
            raise ValueError(f"攻击类型 '{target_attack}' 不存在")
        
        similarities = similarity_matrix.loc[target_attack].sort_values(ascending=False)
        
        # 排除自身
        similarities = similarities[similarities.index != target_attack]
        
        return [(attack, score) for attack, score in similarities.head(top_k).items()]
    
    def save_embeddings(self, filepath: str = 'attack_embeddings.npz'):
        """
        保存嵌入向量到文件
        
        Args:
            filepath: 保存路径
        """
        if not self.attack_embeddings:
            raise ValueError("没有可保存的嵌入向量，请先生成嵌入")
        
        # 准备保存数据
        attack_types = list(self.attack_embeddings.keys())
        embeddings = np.array([self.attack_embeddings[attack] for attack in attack_types])
        
        np.savez(filepath, 
                embeddings=embeddings, 
                attack_types=attack_types,
                model_name=self.model_name)
        
        logger.info(f"嵌入向量已保存到 {filepath}")
    
    def load_embeddings(self, filepath: str = 'attack_embeddings.npz'):
        """
        从文件加载嵌入向量
        
        Args:
            filepath: 文件路径
        """
        data = np.load(filepath, allow_pickle=True)
        
        attack_types = data['attack_types']
        embeddings = data['embeddings']
        
        self.attack_embeddings = {
            attack_type: embedding 
            for attack_type, embedding in zip(attack_types, embeddings)
        }
        
        logger.info(f"从 {filepath} 加载了 {len(self.attack_embeddings)} 种攻击类型的嵌入向量")


def demonstrate_attack_embedding():
    """演示攻击描述嵌入的使用"""
    print("🔤 攻击描述嵌入演示")
    print("="*60)
    
    # 初始化嵌入器
    embedder = AttackDescriptionEmbedder()
    
    # 加载描述和生成嵌入
    embedder.load_attack_descriptions()
    embedder.generate_embeddings()
    
    # 显示相似度分析
    print("\n📊 攻击类型相似度分析:")
    similarity_matrix = embedder.get_attack_similarity_matrix()
    print(similarity_matrix.round(3))
    
    # 找到最相似的攻击类型
    print("\n🔍 攻击类型相似度排名:")
    for attack in ['Exploits', 'DoS', 'Reconnaissance']:
        similar_attacks = embedder.find_most_similar_attacks(attack, top_k=3)
        print(f"\n{attack} 最相似的攻击类型:")
        for similar_attack, score in similar_attacks:
            print(f"  - {similar_attack}: {score:.3f}")
    
    # 保存嵌入
    embedder.save_embeddings()
    print(f"\n💾 嵌入向量已保存")


if __name__ == "__main__":
    demonstrate_attack_embedding()
