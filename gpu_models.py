"""
GPU-Accelerated Model Implementation for Zero-Shot Learning NIDS

This module implements GPU-accelerated versions of the models for complete training:
1. PyTorch-based MLP with GPU support
2. Enhanced Random Forest with parallel processing
3. Full dataset training capabilities

Based on: "From Zero-Shot Machine Learning to Zero-Day Attack Detection"
International Journal of Information Security, 2023
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
import time
import warnings
from tqdm import tqdm
import matplotlib.pyplot as plt

warnings.filterwarnings('ignore')


class GPUEnhancedMLP(nn.Module):
    """
    GPU-accelerated MLP implementation using PyTorch
    
    Configuration as per paper:
    - 2 hidden layers with 100 neurons each
    - ReLU activation function
    - Adam optimizer with learning rate 0.001
    - L2 regularization (weight decay)
    - Full training capability with GPU acceleration
    """
    
    def __init__(self, input_size=41, hidden_size=100, num_classes=2, dropout_rate=0.0):
        super(GPUEnhancedMLP, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_size, num_classes)
        )
        
        # Initialize weights
        self._initialize_weights()
        
        # Training tracking
        self.training_history = {
            'loss': [],
            'accuracy': [],
            'val_loss': [],
            'val_accuracy': []
        }
        
    def _initialize_weights(self):
        """Initialize network weights"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        return self.network(x)


class GPUMLPTrainer:
    """
    GPU-accelerated trainer for MLP model
    """
    
    def __init__(self, input_size=41, hidden_size=100, num_classes=2, 
                 learning_rate=0.001, weight_decay=0.0001, device=None):
        """
        Initialize the GPU MLP trainer
        
        Args:
            input_size: Number of input features
            hidden_size: Number of neurons in hidden layers
            num_classes: Number of output classes
            learning_rate: Learning rate for Adam optimizer
            weight_decay: L2 regularization parameter
            device: Device to use ('cuda' or 'cpu')
        """
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        print(f"🔧 使用设备: {self.device}")
        if self.device.type == 'cuda':
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
            print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        
        # Initialize model
        self.model = GPUEnhancedMLP(input_size, hidden_size, num_classes).to(self.device)
        
        # Initialize optimizer and loss function
        self.optimizer = optim.Adam(
            self.model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay
        )
        self.criterion = nn.CrossEntropyLoss()
        
        # Training parameters
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.is_trained = False
        
    def prepare_data(self, X_train, y_train, X_val=None, y_val=None, batch_size=1024):
        """
        Prepare data for GPU training
        
        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features (optional)
            y_val: Validation labels (optional)
            batch_size: Batch size for training
            
        Returns:
            DataLoader objects for training and validation
        """
        # Convert to tensors (handle DataFrame/Series)
        if hasattr(X_train, 'values'):
            X_train = X_train.values
        if hasattr(y_train, 'values'):
            y_train = y_train.values

        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.LongTensor(y_train).to(self.device)
        
        # Create training dataset and dataloader
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        val_loader = None
        if X_val is not None and y_val is not None:
            # Convert validation data (handle DataFrame/Series)
            if hasattr(X_val, 'values'):
                X_val = X_val.values
            if hasattr(y_val, 'values'):
                y_val = y_val.values

            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            y_val_tensor = torch.LongTensor(y_val).to(self.device)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        return train_loader, val_loader
    
    def train(self, X_train, y_train, X_val=None, y_val=None, 
              epochs=50, batch_size=1024, patience=10, verbose=True):
        """
        Train the MLP model with GPU acceleration
        
        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features
            y_val: Validation labels
            epochs: Number of training epochs
            batch_size: Batch size
            patience: Early stopping patience
            verbose: Whether to print training progress
            
        Returns:
            dict: Training results and statistics
        """
        if verbose:
            print(f"\n🧠 GPU加速MLP训练")
            print("="*60)
            print(f"模型配置:")
            print(f"  - 隐藏层: {self.model.network[0].in_features} -> 100 -> 100 -> {self.model.network[-1].out_features}")
            print(f"  - 激活函数: ReLU")
            print(f"  - 优化器: Adam (lr={self.learning_rate})")
            print(f"  - L2正则化: {self.weight_decay}")
            print(f"  - 设备: {self.device}")
            print(f"\n训练参数:")
            print(f"  - 训练样本: {X_train.shape[0]:,}")
            print(f"  - 特征数: {X_train.shape[1]}")
            print(f"  - 批次大小: {batch_size}")
            print(f"  - 最大轮数: {epochs}")
            print(f"  - 早停耐心值: {patience}")
        
        # Prepare data
        train_loader, val_loader = self.prepare_data(
            X_train, y_train, X_val, y_val, batch_size
        )
        
        # Training loop
        start_time = time.time()
        best_val_loss = float('inf')
        patience_counter = 0
        
        if verbose:
            print(f"\n🔄 开始训练...")
            
        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            # Progress bar for training
            if verbose:
                pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs}', 
                           leave=False, ncols=100)
            else:
                pbar = train_loader
            
            for batch_X, batch_y in pbar:
                # Forward pass
                outputs = self.model(batch_X)
                loss = self.criterion(outputs, batch_y)
                
                # Backward pass
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()
                
                # Statistics
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += batch_y.size(0)
                train_correct += (predicted == batch_y).sum().item()
                
                if verbose:
                    pbar.set_postfix({
                        'Loss': f'{loss.item():.4f}',
                        'Acc': f'{100.*train_correct/train_total:.2f}%'
                    })
            
            # Calculate training metrics
            train_loss /= len(train_loader)
            train_accuracy = 100. * train_correct / train_total
            
            # Validation phase
            val_loss = 0.0
            val_accuracy = 0.0
            if val_loader is not None:
                self.model.eval()
                val_correct = 0
                val_total = 0
                
                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        outputs = self.model(batch_X)
                        loss = self.criterion(outputs, batch_y)
                        
                        val_loss += loss.item()
                        _, predicted = torch.max(outputs.data, 1)
                        val_total += batch_y.size(0)
                        val_correct += (predicted == batch_y).sum().item()
                
                val_loss /= len(val_loader)
                val_accuracy = 100. * val_correct / val_total
                
                # Early stopping check
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # Save best model
                    torch.save(self.model.state_dict(), 'best_model.pth')
                else:
                    patience_counter += 1
            
            # Record history
            self.model.training_history['loss'].append(train_loss)
            self.model.training_history['accuracy'].append(train_accuracy)
            if val_loader is not None:
                self.model.training_history['val_loss'].append(val_loss)
                self.model.training_history['val_accuracy'].append(val_accuracy)
            
            # Print epoch results
            if verbose:
                if val_loader is not None:
                    print(f"Epoch {epoch+1:3d}: "
                          f"Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.2f}% | "
                          f"Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.2f}%")
                    
                    if patience_counter >= patience:
                        print(f"⚠️ 早停触发 (耐心值: {patience})")
                        break
                else:
                    print(f"Epoch {epoch+1:3d}: "
                          f"Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.2f}%")
        
        # Load best model if validation was used
        if val_loader is not None and patience_counter < patience:
            self.model.load_state_dict(torch.load('best_model.pth'))
        
        training_time = time.time() - start_time
        self.is_trained = True
        
        if verbose:
            print(f"\n✅ 训练完成!")
            print(f"   总训练时间: {training_time:.2f}秒")
            print(f"   最终训练准确率: {train_accuracy:.2f}%")
            if val_loader is not None:
                print(f"   最终验证准确率: {val_accuracy:.2f}%")
        
        return {
            'training_time': training_time,
            'final_train_accuracy': train_accuracy,
            'final_val_accuracy': val_accuracy if val_loader is not None else None,
            'training_history': self.model.training_history,
            'epochs_trained': epoch + 1,
            'early_stopped': patience_counter >= patience
        }
    
    def predict(self, X_test):
        """
        Make predictions on test data
        
        Args:
            X_test: Test features
            
        Returns:
            numpy array: Predicted labels
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        self.model.eval()

        # Convert test data (handle DataFrame/Series)
        if hasattr(X_test, 'values'):
            X_test = X_test.values

        X_test_tensor = torch.FloatTensor(X_test).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(X_test_tensor)
            _, predicted = torch.max(outputs, 1)
            
        return predicted.cpu().numpy()
    
    def predict_proba(self, X_test):
        """
        Get prediction probabilities
        
        Args:
            X_test: Test features
            
        Returns:
            numpy array: Prediction probabilities
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        self.model.eval()

        # Convert test data (handle DataFrame/Series)
        if hasattr(X_test, 'values'):
            X_test = X_test.values

        X_test_tensor = torch.FloatTensor(X_test).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(X_test_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            
        return probabilities.cpu().numpy()


class FullDatasetRandomForest:
    """
    Enhanced Random Forest for full dataset training
    """
    
    def __init__(self, n_estimators=50, random_state=42, n_jobs=-1):
        """
        Initialize Random Forest for full dataset training
        
        Args:
            n_estimators: Number of trees (paper uses 50)
            random_state: Random state for reproducibility
            n_jobs: Number of parallel jobs (-1 for all cores)
        """
        self.model = RandomForestClassifier(
            n_estimators=n_estimators,
            criterion='gini',
            max_depth=None,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=random_state,
            n_jobs=n_jobs,
            verbose=1  # Show progress
        )
        self.training_time = 0
        self.is_trained = False
        
    def train(self, X_train, y_train, verbose=True):
        """
        Train Random Forest on full dataset
        
        Args:
            X_train: Training features
            y_train: Training labels
            verbose: Whether to print progress
            
        Returns:
            dict: Training results
        """
        if verbose:
            print(f"\n🌲 完整数据集随机森林训练")
            print("="*60)
            print(f"配置:")
            print(f"  - 决策树数量: {self.model.n_estimators}")
            print(f"  - 并行作业数: {self.model.n_jobs}")
            print(f"  - 训练样本: {X_train.shape[0]:,}")
            print(f"  - 特征数: {X_train.shape[1]}")
            print(f"\n🔄 开始训练...")
        
        start_time = time.time()
        self.model.fit(X_train, y_train)
        self.training_time = time.time() - start_time
        self.is_trained = True
        
        if verbose:
            print(f"✅ 训练完成! 用时: {self.training_time:.2f}秒")
        
        return {
            'training_time': self.training_time,
            'feature_importance': self.model.feature_importances_
        }
    
    def predict(self, X_test):
        """Make predictions"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        return self.model.predict(X_test)
    
    def predict_proba(self, X_test):
        """Get prediction probabilities"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        return self.model.predict_proba(X_test)


def check_gpu_availability():
    """
    Check GPU availability and print system information
    """
    print("🔍 GPU可用性检查")
    print("="*50)
    
    if torch.cuda.is_available():
        print(f"✅ CUDA可用")
        print(f"   GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
            props = torch.cuda.get_device_properties(i)
            print(f"           显存: {props.total_memory / 1e9:.1f} GB")
            print(f"           计算能力: {props.major}.{props.minor}")
        
        # Memory info
        print(f"\n💾 当前显存使用:")
        print(f"   已分配: {torch.cuda.memory_allocated() / 1e9:.2f} GB")
        print(f"   已缓存: {torch.cuda.memory_reserved() / 1e9:.2f} GB")
        
        return True
    else:
        print("❌ CUDA不可用，将使用CPU训练")
        print("   建议安装CUDA版本的PyTorch以获得更好性能")
        return False


if __name__ == '__main__':
    # Test GPU availability
    gpu_available = check_gpu_availability()
    
    # Test with sample data
    print(f"\n🧪 测试GPU模型实现")
    from sklearn.datasets import make_classification
    
    X, y = make_classification(
        n_samples=50000,  # Larger dataset for testing
        n_features=41,
        n_classes=2,
        random_state=42
    )
    
    # Split data
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    # Test GPU MLP
    if gpu_available:
        print(f"\n🧠 测试GPU MLP...")
        gpu_mlp = GPUMLPTrainer()
        results = gpu_mlp.train(X_train, y_train, epochs=10, verbose=True)
        predictions = gpu_mlp.predict(X_test)
        accuracy = accuracy_score(y_test, predictions)
        print(f"   测试准确率: {accuracy:.4f}")
    
    print(f"\n✅ GPU模型测试完成!")
