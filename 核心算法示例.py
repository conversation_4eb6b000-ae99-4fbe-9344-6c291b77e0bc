#!/usr/bin/env python3
"""
零样本学习零日攻击检测核心算法示例
演示项目的核心理论和实现逻辑
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler, LabelEncoder

class ZeroShotAttackDetector:
    """
    零样本学习零日攻击检测器
    
    核心思想：
    1. 使用已知攻击类型训练二分类器（正常 vs 攻击）
    2. 利用学到的"攻击性"特征模式检测未知的零日攻击
    3. 假设：零日攻击虽然是新类型，但仍具有攻击的共同特征
    """
    
    def __init__(self, model_type='rf'):
        """
        初始化检测器
        
        Args:
            model_type: 'rf' for Random Forest, 'mlp' for MLP
        """
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        
        # 初始化模型
        if model_type == 'rf':
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=20,
                random_state=42
            )
        elif model_type == 'mlp':
            self.model = MLPClassifier(
                hidden_layer_sizes=(100, 50),
                max_iter=500,
                random_state=42
            )
    
    def create_zero_shot_split(self, X, y_attack, zero_day_attack):
        """
        创建零样本学习数据划分
        
        核心逻辑：
        - 训练集：排除零日攻击类型，只包含已知攻击+正常流量
        - 测试集：包含所有类型，包括零日攻击
        
        Args:
            X: 特征数据
            y_attack: 攻击类型标签
            zero_day_attack: 要模拟的零日攻击类型
            
        Returns:
            训练和测试数据
        """
        print(f"\n🎯 零样本学习数据划分")
        print(f"零日攻击类型: {zero_day_attack}")
        
        # 创建二分类标签 (0=正常, 1=攻击)
        y_binary = (y_attack != 'Normal').astype(int)
        
        # 训练集：排除零日攻击
        train_mask = y_attack != zero_day_attack
        X_train = X[train_mask]
        y_train_binary = y_binary[train_mask]
        y_train_attack = y_attack[train_mask]
        
        # 测试集：包含所有数据
        X_test = X.copy()
        y_test_binary = y_binary.copy()
        y_test_attack = y_attack.copy()
        
        # 零日攻击掩码
        zero_day_mask = y_attack == zero_day_attack
        
        print(f"训练集大小: {len(X_train)}")
        print(f"测试集大小: {len(X_test)}")
        print(f"零日攻击样本数: {zero_day_mask.sum()}")
        
        # 训练集攻击类型分布
        print(f"\n训练集攻击类型分布:")
        train_dist = y_train_attack.value_counts()
        for attack_type, count in train_dist.items():
            print(f"  {attack_type}: {count}")
        
        return (X_train, y_train_binary, X_test, y_test_binary, 
                y_test_attack, zero_day_mask)
    
    def train(self, X_train, y_train_binary):
        """
        训练二分类器学习攻击性特征
        
        核心思想：
        - 不关注具体攻击类型，只学习"攻击 vs 正常"的边界
        - 提取所有已知攻击的共同特征模式
        - 建立泛化的攻击检测能力
        """
        print(f"\n🚀 训练{self.model_type.upper()}模型...")
        
        # 特征标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # 训练二分类器
        self.model.fit(X_train_scaled, y_train_binary)
        self.is_trained = True
        
        # 训练集性能
        train_pred = self.model.predict(X_train_scaled)
        train_accuracy = accuracy_score(y_train_binary, train_pred)
        
        print(f"训练完成!")
        print(f"训练集准确率: {train_accuracy:.4f}")
        
        return train_accuracy
    
    def detect_zero_day(self, X_test, y_test_binary, y_test_attack, zero_day_mask):
        """
        检测零日攻击
        
        核心逻辑：
        1. 使用训练好的二分类器对测试数据进行预测
        2. 如果零日攻击被正确分类为"攻击"，说明检测成功
        3. 计算零日检测率 (Z-DR) 作为主要评估指标
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练!")
        
        print(f"\n🔍 零日攻击检测...")
        
        # 特征标准化
        X_test_scaled = self.scaler.transform(X_test)
        
        # 模型预测
        y_pred_binary = self.model.predict(X_test_scaled)
        y_pred_proba = self.model.predict_proba(X_test_scaled)
        
        # 整体性能
        overall_accuracy = accuracy_score(y_test_binary, y_pred_binary)
        
        # 零日检测率 (Z-DR) 计算
        zero_day_predictions = y_pred_binary[zero_day_mask]
        zero_day_detected = (zero_day_predictions == 1).sum()
        zero_day_total = zero_day_mask.sum()
        z_dr = (zero_day_detected / zero_day_total * 100) if zero_day_total > 0 else 0
        
        # 误报率 (FAR) 计算
        normal_mask = y_test_binary == 0
        normal_predictions = y_pred_binary[normal_mask]
        false_alarms = (normal_predictions == 1).sum()
        total_normal = normal_mask.sum()
        far = (false_alarms / total_normal * 100) if total_normal > 0 else 0
        
        print(f"检测结果:")
        print(f"  整体准确率: {overall_accuracy:.4f}")
        print(f"  零日检测率 (Z-DR): {z_dr:.2f}%")
        print(f"  误报率 (FAR): {far:.2f}%")
        print(f"  零日攻击样本: {zero_day_total}")
        print(f"  成功检测: {zero_day_detected}")
        print(f"  漏检: {zero_day_total - zero_day_detected}")
        
        return {
            'overall_accuracy': overall_accuracy,
            'z_dr': z_dr,
            'far': far,
            'zero_day_detected': zero_day_detected,
            'zero_day_total': zero_day_total,
            'predictions': y_pred_binary,
            'probabilities': y_pred_proba
        }

def demonstrate_zero_shot_learning():
    """
    演示零样本学习的核心概念
    """
    print("🧪 零样本学习零日攻击检测演示")
    print("="*60)
    
    # 模拟UNSW-NB15数据集
    print("📊 生成模拟数据...")
    np.random.seed(42)
    
    # 特征数据 (模拟网络流量特征)
    n_samples = 1000
    n_features = 10
    X = np.random.randn(n_samples, n_features)
    
    # 攻击类型标签
    attack_types = ['Normal', 'Exploits', 'DoS', 'Fuzzers', 'Reconnaissance']
    y_attack = np.random.choice(attack_types, size=n_samples, 
                               p=[0.6, 0.15, 0.1, 0.1, 0.05])
    
    # 为不同攻击类型添加特征模式
    for i, attack in enumerate(attack_types[1:], 1):  # 跳过Normal
        mask = y_attack == attack
        # 攻击类型有特定的特征模式
        X[mask] += np.random.normal(i*2, 0.5, (mask.sum(), n_features))
    
    print(f"数据集大小: {n_samples}")
    print(f"特征数: {n_features}")
    print(f"攻击类型分布:")
    for attack_type in attack_types:
        count = (y_attack == attack_type).sum()
        print(f"  {attack_type}: {count}")
    
    # 零样本学习实验
    zero_day_attacks = ['Exploits', 'DoS', 'Fuzzers']  # 轮流作为零日攻击
    
    results = {}
    
    for zero_day_attack in zero_day_attacks:
        print(f"\n" + "="*60)
        print(f"🎯 零日攻击模拟: {zero_day_attack}")
        print("="*60)
        
        # 初始化检测器
        detector = ZeroShotAttackDetector(model_type='rf')
        
        # 创建零样本数据划分
        (X_train, y_train_binary, X_test, y_test_binary, 
         y_test_attack, zero_day_mask) = detector.create_zero_shot_split(
            X, y_attack, zero_day_attack
        )
        
        # 训练模型
        train_accuracy = detector.train(X_train, y_train_binary)
        
        # 检测零日攻击
        detection_results = detector.detect_zero_day(
            X_test, y_test_binary, y_test_attack, zero_day_mask
        )
        
        results[zero_day_attack] = detection_results
    
    # 总结结果
    print(f"\n" + "="*60)
    print("📊 零样本学习实验总结")
    print("="*60)
    
    avg_z_dr = np.mean([r['z_dr'] for r in results.values()])
    avg_far = np.mean([r['far'] for r in results.values()])
    avg_accuracy = np.mean([r['overall_accuracy'] for r in results.values()])
    
    print(f"平均零日检测率 (Z-DR): {avg_z_dr:.2f}%")
    print(f"平均误报率 (FAR): {avg_far:.2f}%")
    print(f"平均整体准确率: {avg_accuracy:.4f}")
    
    print(f"\n各攻击类型详细结果:")
    for attack_type, result in results.items():
        print(f"  {attack_type}:")
        print(f"    Z-DR: {result['z_dr']:.2f}%")
        print(f"    FAR: {result['far']:.2f}%")
        print(f"    检测: {result['zero_day_detected']}/{result['zero_day_total']}")
    
    # 成功标准判断
    print(f"\n🎯 性能评估:")
    if avg_z_dr >= 90:
        print("🎉 优秀! 零日检测率 ≥ 90%")
    elif avg_z_dr >= 80:
        print("✅ 良好! 零日检测率 ≥ 80%")
    elif avg_z_dr >= 70:
        print("👍 可接受! 零日检测率 ≥ 70%")
    else:
        print("⚠️ 需要改进! 零日检测率 < 70%")
    
    if avg_far <= 5:
        print("✅ 误报率控制良好 ≤ 5%")
    elif avg_far <= 10:
        print("👍 误报率可接受 ≤ 10%")
    else:
        print("⚠️ 误报率偏高 > 10%")

if __name__ == "__main__":
    demonstrate_zero_shot_learning()
