"""
Model Implementation Module for Zero-Shot Learning NIDS

This module implements the specific model configurations described in:
"From Zero-Shot Machine Learning to Zero-Day Attack Detection"
International Journal of Information Security, 2023

Models:
1. Random Forest: 50 trees, Gini impurity, no max depth
2. MLP: 2 hidden layers (100 neurons each), ReLU, Adam optimizer
3. XGBoost: Gradient boosting with optimized hyperparameters
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import time
import warnings
warnings.filterwarnings('ignore')

# XGBoost import with error handling
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost not installed. Please install with: pip install xgboost")

# LightGBM import with error handling
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("⚠️ LightGBM not installed. Please install with: pip install lightgbm")

# PyTorch import for Autoencoder implementation
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    print("⚠️ PyTorch not installed. Please install with: pip install torch")


class EnhancedRandomForest:
    """
    Enhanced Random Forest implementation with monitoring and analysis
    
    Configuration as per paper:
    - 50 decision trees
    - Gini impurity criterion
    - No maximum depth limit
    - min_samples_split=2, min_samples_leaf=1
    """
    
    def __init__(self, random_state=42):
        """Initialize the Enhanced Random Forest"""
        self.model = RandomForestClassifier(
            n_estimators=50,
            criterion='gini',
            max_depth=None,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=random_state,
            n_jobs=-1  # Use all available cores
        )
        self.training_time = 0
        self.feature_importance = None
        self.is_trained = False
    
    def train(self, X_train, y_train, verbose=True):
        """
        Train the Random Forest model with real-time monitoring

        Args:
            X_train: Training features
            y_train: Training labels
            verbose: Whether to print training information

        Returns:
            dict: Training results and statistics
        """
        if verbose:
            print(f"\n🌲 训练随机森林模型")
            print("="*50)
            print(f"配置参数:")
            print(f"  - 决策树数量: {self.model.n_estimators}")
            print(f"  - 分割准则: {self.model.criterion}")
            print(f"  - 最大深度: {self.model.max_depth}")
            print(f"  - 最小分割样本数: {self.model.min_samples_split}")
            print(f"  - 最小叶子样本数: {self.model.min_samples_leaf}")
            print(f"\n训练数据:")
            print(f"  - 样本数: {X_train.shape[0]:,}")
            print(f"  - 特征数: {X_train.shape[1]}")
            print(f"  - 类别数: {len(np.unique(y_train))}")
            print(f"\n🔄 开始训练，启用进度监控...")

        # Record training time
        start_time = time.time()

        # Train with progress monitoring
        if verbose:
            # Create a custom RF with warm_start for progress monitoring
            progress_model = RandomForestClassifier(
                n_estimators=1,  # Start with 1 tree
                criterion=self.model.criterion,
                max_depth=self.model.max_depth,
                min_samples_split=self.model.min_samples_split,
                min_samples_leaf=self.model.min_samples_leaf,
                random_state=self.model.random_state,
                warm_start=True,
                n_jobs=1  # Single job for progress monitoring
            )

            # 创建进度条函数
            def create_progress_bar(current, total, width=30):
                progress = current / total
                filled = int(width * progress)
                bar = '█' * filled + '░' * (width - filled)
                return f"[{bar}] {progress*100:6.1f}%"

            # 增量训练决策树并显示进度
            print(f"  📊 训练进度:")
            for i in range(1, self.model.n_estimators + 1):
                progress_model.n_estimators = i
                progress_model.fit(X_train, y_train)

                # 计算进度
                elapsed = time.time() - start_time
                progress_bar = create_progress_bar(i, self.model.n_estimators)

                # 每10棵树或最后一棵树显示进度
                if i % 10 == 0 or i == self.model.n_estimators:
                    print(f"  {progress_bar} 第{i:2d}/{self.model.n_estimators}棵树 | 时间: {elapsed:.1f}s")
                elif i <= 5:  # 前5棵树也显示
                    print(f"  {progress_bar} 第{i:2d}/{self.model.n_estimators}棵树 | 时间: {elapsed:.1f}s")

            # Copy the trained model
            self.model = progress_model
        else:
            # Train normally without progress monitoring
            self.model.fit(X_train, y_train)

        # Calculate training time
        self.training_time = time.time() - start_time
        self.is_trained = True

        # Extract feature importance
        self.feature_importance = self.model.feature_importances_

        if verbose:
            print(f"\n✅ 训练完成!")
            print(f"  - 总训练时间: {self.training_time:.2f} 秒")
            print(f"  - 构建决策树数: {len(self.model.estimators_)}")
            print(f"  - 平均每棵树时间: {self.training_time/len(self.model.estimators_):.3f}秒")
            print(f"  - 特征重要性计算: ✓")

        return {
            'training_time': self.training_time,
            'n_estimators': len(self.model.estimators_),
            'feature_importance': self.feature_importance,
            'model_size': self._estimate_model_size(),
            'avg_time_per_tree': self.training_time / len(self.model.estimators_)
        }
    
    def predict(self, X):
        """Make predictions"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        return self.model.predict(X)
    
    def predict_proba(self, X):
        """Get prediction probabilities"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        return self.model.predict_proba(X)
    
    def get_feature_importance(self, feature_names=None, top_n=10):
        """
        Get feature importance analysis
        
        Args:
            feature_names: List of feature names
            top_n: Number of top features to return
            
        Returns:
            dict: Feature importance analysis
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before analyzing feature importance")
        
        if feature_names is None:
            feature_names = [f"feature_{i}" for i in range(len(self.feature_importance))]
        
        # Create feature importance dataframe
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': self.feature_importance
        }).sort_values('importance', ascending=False)
        
        print(f"\n📊 特征重要性分析")
        print("="*40)
        print(f"前 {top_n} 个最重要特征:")

        for i, (_, row) in enumerate(importance_df.head(top_n).iterrows(), 1):
            print(f"  {i:2d}. {row['feature']:<20} {row['importance']:.4f}")
        
        return {
            'importance_scores': self.feature_importance,
            'top_features': importance_df.head(top_n),
            'all_features': importance_df
        }
    
    def _estimate_model_size(self):
        """Estimate model size in memory"""
        # Rough estimation based on number of trees and nodes
        total_nodes = sum(tree.tree_.node_count for tree in self.model.estimators_)
        estimated_size_mb = total_nodes * 64 / (1024 * 1024)  # Rough estimate
        return estimated_size_mb


class EnhancedMLP:
    """
    Enhanced Multi-Layer Perceptron implementation with monitoring
    
    Configuration as per paper:
    - 2 hidden layers with 100 neurons each
    - ReLU activation function
    - Adam optimizer with learning rate 0.001
    - L2 regularization parameter 0.0001
    - Maximum 5 iterations (调整为快速开发模式)
    """
    
    def __init__(self, random_state=42, fast_mode=False):
        """Initialize the Enhanced MLP"""
        if fast_mode:
            # Optimized configuration for faster training
            self.model = MLPClassifier(
                hidden_layer_sizes=(100, 100),
                activation='relu',
                solver='adam',
                learning_rate_init=0.001,
                alpha=0.0001,  # L2 regularization
                max_iter=5,  # 快速模式：只训练5轮
                random_state=random_state,
                early_stopping=True,  # Enable early stopping for speed
                validation_fraction=0.1,  # Use 10% for validation
                n_iter_no_change=3,  # Stop if no improvement for 3 iterations
                tol=1e-4,  # Tolerance for optimization
                batch_size='auto'  # Automatic batch size
            )
        else:
            # Original paper configuration
            self.model = MLPClassifier(
                hidden_layer_sizes=(100, 100),
                activation='relu',
                solver='adam',
                learning_rate_init=0.001,
                alpha=0.0001,  # L2 regularization
                max_iter=5,  # 调整为5轮以加快开发速度
                random_state=random_state,
                early_stopping=False,  # Disable early stopping to match paper
                validation_fraction=0.0  # No validation split
            )
        self.training_time = 0
        self.loss_curve = None
        self.is_trained = False
        self.convergence_info = {}
    
    def train(self, X_train, y_train, verbose=True):
        """
        训练MLP模型，带有实时监控和收敛分析

        Args:
            X_train: 训练特征
            y_train: 训练标签
            verbose: 是否打印训练信息

        Returns:
            dict: 训练结果和统计信息
        """
        if verbose:
            print(f"\n🧠 训练多层感知机模型")
            print("="*50)
            print(f"配置参数:")
            print(f"  - 隐藏层: {self.model.hidden_layer_sizes}")
            print(f"  - 激活函数: {self.model.activation}")
            print(f"  - 优化器: {self.model.solver}")
            print(f"  - 学习率: {self.model.learning_rate_init}")
            print(f"  - L2正则化 (alpha): {self.model.alpha}")
            print(f"  - 最大迭代次数: {self.model.max_iter}")
            print(f"\n训练数据:")
            print(f"  - 样本数: {X_train.shape[0]:,}")
            print(f"  - 特征数: {X_train.shape[1]}")
            print(f"  - 类别数: {len(np.unique(y_train))}")
            print(f"\n🔄 开始训练，启用收敛监控...")

        # 记录训练时间
        start_time = time.time()

        # 带监控的训练
        if verbose:
            # 使用自定义监控方法进行增量训练
            monitor_model = MLPClassifier(
                hidden_layer_sizes=self.model.hidden_layer_sizes,
                activation=self.model.activation,
                solver=self.model.solver,
                learning_rate_init=self.model.learning_rate_init,
                alpha=self.model.alpha,
                max_iter=1,  # 每次训练一个迭代
                random_state=self.model.random_state,
                warm_start=True
            )

            loss_history = []
            convergence_threshold = 1e-4
            patience = 5
            no_improvement_count = 0
            best_loss = float('inf')

            print(f"  📊 监控收敛 (阈值: {convergence_threshold}, 耐心值: {patience})...")

            # 创建进度条字符
            def create_progress_bar(current, total, width=30):
                progress = current / total
                filled = int(width * progress)
                bar = '█' * filled + '░' * (width - filled)
                return f"[{bar}] {progress*100:6.1f}%"

            for iteration in range(1, self.model.max_iter + 1):
                # 训练一个迭代
                monitor_model.max_iter = iteration
                monitor_model.fit(X_train, y_train)

                # 获取当前损失
                current_loss = monitor_model.loss_
                loss_history.append(current_loss)

                # 检查改进
                if current_loss < best_loss - convergence_threshold:
                    best_loss = current_loss
                    no_improvement_count = 0
                else:
                    no_improvement_count += 1

                # 计算进度
                elapsed = time.time() - start_time
                progress_bar = create_progress_bar(iteration, self.model.max_iter)

                # 显示进度 - 每次迭代都显示，但使用\r覆盖上一行
                if iteration == 1:
                    print(f"  📈 训练进度:")

                print(f"\r  {progress_bar} 第{iteration:2d}/{self.model.max_iter}轮 | "
                      f"损失: {current_loss:.6f} | 时间: {elapsed:5.1f}s", end='', flush=True)

                # 每5轮或关键点换行显示详细信息
                if iteration % 5 == 0 or iteration <= 3 or no_improvement_count >= patience or iteration == self.model.max_iter:
                    print()  # 换行
                    if no_improvement_count > 0:
                        print(f"    ⚠️ 连续{no_improvement_count}轮无改进")

                # 早停检查
                if no_improvement_count >= patience:
                    print(f"\n  🛑 早停: 连续{patience}轮无改进")
                    break

                # 收敛检查
                if current_loss < convergence_threshold:
                    print(f"\n  ✅ 已收敛: 损失低于阈值 ({convergence_threshold})")
                    break

            if iteration == self.model.max_iter and no_improvement_count < patience:
                print()  # 确保最后换行

            # 复制训练好的模型
            self.model = monitor_model
            self.loss_curve = loss_history
        else:
            # 正常训练，无监控
            self.model.fit(X_train, y_train)
            self.loss_curve = self.model.loss_curve_ if hasattr(self.model, 'loss_curve_') else None

        # Calculate training time
        self.training_time = time.time() - start_time
        self.is_trained = True

        # Extract training information
        self.convergence_info = {
            'n_iter': self.model.n_iter_,
            'converged': self.model.n_iter_ < self.model.max_iter,
            'final_loss': self.loss_curve[-1] if self.loss_curve else None,
            'loss_improvement': self._analyze_loss_improvement(),
            'convergence_rate': self._calculate_convergence_rate()
        }

        if verbose:
            print(f"\n✅ 训练完成!")
            print(f"  - 总训练时间: {self.training_time:.2f} 秒")
            print(f"  - 完成迭代数: {self.convergence_info['n_iter']}")
            print(f"  - 是否收敛: {'是' if self.convergence_info['converged'] else '否'}")
            if self.convergence_info['final_loss']:
                print(f"  - 最终损失: {self.convergence_info['final_loss']:.6f}")
                print(f"  - 损失改进: {self.convergence_info['loss_improvement']:.6f}")
                print(f"  - 收敛速率: {self.convergence_info['convergence_rate']:.6f}")

        return {
            'training_time': self.training_time,
            'n_iterations': self.convergence_info['n_iter'],
            'converged': self.convergence_info['converged'],
            'final_loss': self.convergence_info['final_loss'],
            'loss_curve': self.loss_curve,
            'loss_improvement': self.convergence_info['loss_improvement'],
            'convergence_rate': self.convergence_info['convergence_rate']
        }

    def _analyze_loss_improvement(self):
        """Analyze the loss improvement during training"""
        if not self.loss_curve or len(self.loss_curve) < 2:
            return 0.0

        initial_loss = self.loss_curve[0]
        final_loss = self.loss_curve[-1]
        return initial_loss - final_loss

    def _calculate_convergence_rate(self):
        """Calculate the convergence rate (loss reduction per iteration)"""
        if not self.loss_curve or len(self.loss_curve) < 2:
            return 0.0

        loss_improvement = self._analyze_loss_improvement()
        return loss_improvement / len(self.loss_curve)
    
    def predict(self, X):
        """Make predictions"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        return self.model.predict(X)
    
    def predict_proba(self, X):
        """Get prediction probabilities"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        return self.model.predict_proba(X)
    
    def plot_learning_curve(self, save_path=None):
        """
        Plot the learning curve (loss over iterations)
        
        Args:
            save_path: Path to save the plot (optional)
        """
        if not self.is_trained or self.loss_curve is None:
            print("⚠️ No loss curve available. Model may not have been trained properly.")
            return
        
        plt.figure(figsize=(10, 6))
        plt.plot(self.loss_curve, 'b-', linewidth=2)
        plt.title('MLP Training Loss Curve')
        plt.xlabel('Iteration')
        plt.ylabel('Loss')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 Learning curve saved to {save_path}")
        
        plt.show()
        
        return plt.gcf()


class EnhancedXGBoost:
    """
    Enhanced XGBoost implementation for Zero-Shot Learning NIDS

    Optimized hyperparameters for network intrusion detection with
    monitoring capabilities and feature importance analysis.
    """

    def __init__(self, fast_mode=False, use_gpu=False):
        """
        Initialize XGBoost with optimized parameters

        Args:
            fast_mode (bool): Use reduced parameters for faster training
            use_gpu (bool): Whether to use GPU acceleration
        """
        if not XGBOOST_AVAILABLE:
            raise ImportError("XGBoost is not installed. Please install with: pip install xgboost")

        self.fast_mode = fast_mode
        self.use_gpu = use_gpu
        self.model = None
        self.feature_importance_ = None
        self.training_history = []
        self.is_trained = False
        self.training_time = 0

        # Check GPU availability
        self.gpu_available = self._check_gpu_availability()

        # Hyperparameters optimized for network intrusion detection
        base_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'logloss',
            'random_state': 42,
            'verbosity': 0
        }

        # GPU-specific parameters
        if self.use_gpu and self.gpu_available:
            base_params.update({
                'tree_method': 'gpu_hist',
                'gpu_id': 0,
                'predictor': 'gpu_predictor'
            })
            print("🔥 启用XGBoost GPU加速")
        else:
            base_params['n_jobs'] = -1
            if self.use_gpu and not self.gpu_available:
                print("⚠️ GPU不可用，使用CPU训练XGBoost")

        if fast_mode:
            self.params = {
                **base_params,
                'max_depth': 4,
                'learning_rate': 0.2,
                'n_estimators': 50,
                'subsample': 0.8,
                'colsample_bytree': 0.8
            }
        else:
            self.params = {
                **base_params,
                'max_depth': 6,
                'learning_rate': 0.1,
                'n_estimators': 200,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 0.1,
                'reg_lambda': 1.0
            }

    def train(self, X_train, y_train, verbose=True):
        """
        Train XGBoost model with monitoring

        Args:
            X_train: Training features
            y_train: Training labels
            verbose: Whether to show training progress

        Returns:
            dict: Training results
        """
        if verbose:
            print(f"\n🚀 训练 XGBoost 模型...")
            print(f"   参数配置: {'快速模式' if self.fast_mode else '完整模式'}")
            print(f"   计算设备: {'GPU' if self.use_gpu and self.gpu_available else 'CPU'}")
            print(f"   训练样本: {X_train.shape[0]:,}")
            print(f"   特征数量: {X_train.shape[1]}")

        start_time = time.time()

        # Create XGBoost model
        self.model = xgb.XGBClassifier(**self.params)

        # Train with evaluation set for monitoring
        eval_set = [(X_train, y_train)]

        self.model.fit(
            X_train, y_train,
            eval_set=eval_set,
            verbose=False
        )

        self.training_time = time.time() - start_time
        self.is_trained = True

        # Get feature importance
        self.feature_importance_ = self.model.feature_importances_

        # Training accuracy
        train_pred = self.model.predict(X_train)
        train_accuracy = accuracy_score(y_train, train_pred)

        results = {
            'training_time': self.training_time,
            'training_accuracy': train_accuracy,
            'n_estimators': self.params['n_estimators'],
            'max_depth': self.params['max_depth'],
            'model_size': self._calculate_model_size()
        }

        if verbose:
            print(f"   ✅ 训练完成!")
            print(f"   ⏱️ 训练时间: {self.training_time:.2f}秒")
            print(f"   📊 训练准确率: {train_accuracy:.4f}")
            print(f"   🌳 树的数量: {self.params['n_estimators']}")
            print(f"   📏 最大深度: {self.params['max_depth']}")

        return results

    def predict(self, X_test):
        """Make predictions"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
        return self.model.predict(X_test)

    def predict_proba(self, X_test):
        """Get prediction probabilities"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
        return self.model.predict_proba(X_test)

    def get_feature_importance(self, feature_names=None, top_k=10):
        """
        Get feature importance analysis

        Args:
            feature_names: List of feature names
            top_k: Number of top features to return

        Returns:
            dict: Feature importance analysis
        """
        if self.feature_importance_ is None:
            raise ValueError("Model not trained yet. Call train() first.")

        if feature_names is None:
            feature_names = [f'feature_{i}' for i in range(len(self.feature_importance_))]

        # Create importance dataframe
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': self.feature_importance_
        }).sort_values('importance', ascending=False)

        return {
            'top_features': importance_df.head(top_k),
            'all_features': importance_df
        }

    def _calculate_model_size(self):
        """Calculate approximate model size in MB"""
        if self.model is None:
            return 0.0

        # Rough estimation based on number of trees and depth
        trees = self.params['n_estimators']
        depth = self.params['max_depth']
        # Each tree node roughly 8 bytes, 2^depth nodes per tree
        size_bytes = trees * (2 ** depth) * 8
        return size_bytes / (1024 * 1024)  # Convert to MB

    def _check_gpu_availability(self):
        """
        Check if GPU is available for XGBoost

        Returns:
            bool: True if GPU is available, False otherwise
        """
        # For now, let's disable GPU auto-detection to avoid hanging
        # and let users manually enable it if they know their setup supports it
        return False

        # TODO: Implement proper GPU detection when XGBoost GPU support is confirmed
        # try:
        #     import subprocess
        #     result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=3)
        #     return result.returncode == 0
        # except:
        #     return False


def compare_models(rf_results, mlp_results, xgb_results=None, lgb_results=None):
    """
    Compare training results between RF, MLP, XGBoost, and LightGBM models

    Args:
        rf_results: Training results from Random Forest
        mlp_results: Training results from MLP
        xgb_results: Training results from XGBoost (optional)
        lgb_results: Training results from LightGBM (optional)
    """
    print(f"\n📊 MODEL COMPARISON")
    print("="*80)

    # Header
    models_count = 2  # RF + MLP
    if xgb_results:
        models_count += 1
    if lgb_results:
        models_count += 1

    header = f"{'Metric':<25} {'Random Forest':<15} {'MLP':<15}"
    if xgb_results:
        header += f" {'XGBoost':<15}"
    if lgb_results:
        header += f" {'LightGBM':<15}"

    print(header)
    print("-" * (25 + 15 * models_count + 5))

    # Training time
    time_line = f"{'Training Time (s)':<25} {rf_results['training_time']:<15.2f} {mlp_results['training_time']:<15.2f}"
    if xgb_results:
        time_line += f" {xgb_results['training_time']:<15.2f}"
    if lgb_results:
        time_line += f" {lgb_results['training_time']:<15.2f}"
    print(time_line)

    # Model size
    if 'model_size' in rf_results:
        if xgb_results and 'model_size' in xgb_results:
            print(f"{'Model Size (MB)':<25} {rf_results['model_size']:<15.2f} {'N/A':<15} {xgb_results['model_size']:<15.2f}")
        else:
            print(f"{'Model Size (MB)':<25} {rf_results['model_size']:<15.2f} {'N/A':<15}")

    # Trees/Iterations
    if xgb_results:
        print(f"{'Trees/Iterations':<25} {rf_results['n_estimators']:<15} {mlp_results['n_iterations']:<15} {xgb_results['n_estimators']:<15}")
    else:
        print(f"{'Trees/Iterations':<25} {rf_results['n_estimators']:<15} {mlp_results['n_iterations']:<15}")

    # Final loss (only for MLP)
    if mlp_results['final_loss']:
        if xgb_results:
            print(f"{'Final Loss':<25} {'N/A':<15} {mlp_results['final_loss']:<15.6f} {'N/A':<15}")
        else:
            print(f"{'Final Loss':<25} {'N/A':<15} {mlp_results['final_loss']:<15.6f}")

    # Convergence (only for MLP)
    if xgb_results:
        print(f"{'Converged':<25} {'N/A':<15} {str(mlp_results['converged']):<15} {'N/A':<15}")
    else:
        print(f"{'Converged':<25} {'N/A':<15} {str(mlp_results['converged']):<15}")

    # Training efficiency comparison
    print(f"\n🏆 TRAINING EFFICIENCY RANKING:")
    times = [
        ('Random Forest', rf_results['training_time']),
        ('MLP', mlp_results['training_time'])
    ]

    if xgb_results:
        times.append(('XGBoost', xgb_results['training_time']))

    # Sort by training time (fastest first)
    times.sort(key=lambda x: x[1])

    for i, (model, time_val) in enumerate(times, 1):
        if i == 1:
            print(f"   {i}. {model}: {time_val:.2f}s (fastest)")
        else:
            speedup = time_val / times[0][1]
            print(f"   {i}. {model}: {time_val:.2f}s ({speedup:.1f}x slower)")

    return times


class EnhancedLightGBM:
    """
    Enhanced LightGBM implementation for Zero-Shot Learning NIDS

    Optimized hyperparameters for network intrusion detection with
    GPU support and monitoring capabilities.
    """

    def __init__(self, fast_mode=False, use_gpu=False):
        """
        Initialize LightGBM with optimized parameters

        Args:
            fast_mode (bool): Use reduced parameters for faster training
            use_gpu (bool): Whether to use GPU acceleration
        """
        if not LIGHTGBM_AVAILABLE:
            raise ImportError("LightGBM is not installed. Please install with: pip install lightgbm")

        self.fast_mode = fast_mode
        self.use_gpu = use_gpu
        self.model = None
        self.feature_importance_ = None
        self.training_history = []
        self.is_trained = False
        self.training_time = 0

        # Check GPU availability
        self.gpu_available = self._check_gpu_availability()

        # Base parameters for LightGBM
        base_params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'random_state': 42,
            'verbosity': -1,
            'force_row_wise': True  # Avoid warnings
        }

        # GPU-specific parameters
        if self.use_gpu and self.gpu_available:
            base_params.update({
                'device': 'gpu',
                'gpu_platform_id': 0,
                'gpu_device_id': 0
            })
            print("🔥 启用LightGBM GPU加速")
        else:
            base_params['device'] = 'cpu'
            base_params['num_threads'] = -1
            if self.use_gpu and not self.gpu_available:
                print("⚠️ GPU不可用，使用CPU训练LightGBM")

        # Mode-specific parameters
        if fast_mode:
            self.params = {
                **base_params,
                'num_leaves': 31,
                'max_depth': 5,
                'learning_rate': 0.1,
                'n_estimators': 50,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'min_child_samples': 20
            }
        else:
            self.params = {
                **base_params,
                'num_leaves': 63,
                'max_depth': 7,
                'learning_rate': 0.05,
                'n_estimators': 200,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'min_child_samples': 20,
                'reg_alpha': 0.1,
                'reg_lambda': 0.1
            }

    def train(self, X_train, y_train, verbose=True):
        """
        Train LightGBM model with monitoring

        Args:
            X_train: Training features
            y_train: Training labels
            verbose: Whether to show training progress

        Returns:
            dict: Training results
        """
        if verbose:
            print(f"\n🚀 训练 LightGBM 模型...")
            print(f"   参数配置: {'快速模式' if self.fast_mode else '完整模式'}")
            print(f"   计算设备: {'GPU' if self.use_gpu and self.gpu_available else 'CPU'}")
            print(f"   训练样本: {X_train.shape[0]:,}")
            print(f"   特征数量: {X_train.shape[1]}")

        start_time = time.time()

        # Create LightGBM model
        self.model = lgb.LGBMClassifier(**self.params)

        # Train the model
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_train, y_train)],
            callbacks=[lgb.log_evaluation(0)]  # Silent training
        )

        self.training_time = time.time() - start_time
        self.is_trained = True

        # Get feature importance
        self.feature_importance_ = self.model.feature_importances_

        # Training accuracy
        train_pred = self.model.predict(X_train)
        train_accuracy = accuracy_score(y_train, train_pred)

        results = {
            'training_time': self.training_time,
            'training_accuracy': train_accuracy,
            'n_estimators': self.params['n_estimators'],
            'num_leaves': self.params['num_leaves'],
            'max_depth': self.params['max_depth'],
            'model_size': self._calculate_model_size()
        }

        if verbose:
            print(f"   ✅ 训练完成!")
            print(f"   ⏱️ 训练时间: {self.training_time:.2f}秒")
            print(f"   📊 训练准确率: {train_accuracy:.4f}")
            print(f"   🌳 树的数量: {self.params['n_estimators']}")
            print(f"   🍃 叶子数量: {self.params['num_leaves']}")
            print(f"   📏 最大深度: {self.params['max_depth']}")

        return results

    def predict(self, X_test):
        """Make predictions"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
        return self.model.predict(X_test)

    def predict_proba(self, X_test):
        """Get prediction probabilities"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")
        return self.model.predict_proba(X_test)

    def get_feature_importance(self, feature_names=None, top_k=10):
        """
        Get feature importance analysis

        Args:
            feature_names: List of feature names
            top_k: Number of top features to return

        Returns:
            dict: Feature importance analysis
        """
        if self.feature_importance_ is None:
            raise ValueError("Model not trained yet. Call train() first.")

        if feature_names is None:
            feature_names = [f'feature_{i}' for i in range(len(self.feature_importance_))]

        # Create importance dataframe
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': self.feature_importance_
        }).sort_values('importance', ascending=False)

        return {
            'top_features': importance_df.head(top_k),
            'all_features': importance_df
        }

    def _calculate_model_size(self):
        """Calculate approximate model size in MB"""
        if self.model is None:
            return 0.0

        # Rough estimation based on number of trees and leaves
        trees = self.params['n_estimators']
        leaves = self.params['num_leaves']
        # Each leaf roughly 16 bytes (split info + prediction)
        size_bytes = trees * leaves * 16
        return size_bytes / (1024 * 1024)  # Convert to MB

    def _check_gpu_availability(self):
        """
        Check if GPU is available for LightGBM

        Returns:
            bool: True if GPU is available, False otherwise
        """
        # For now, let's disable GPU auto-detection to avoid hanging
        # LightGBM GPU support requires specific OpenCL setup
        return False

        # TODO: Implement proper GPU detection when LightGBM GPU support is confirmed
        # try:
        #     import subprocess
        #     result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=3)
        #     if result.returncode != 0:
        #         return False
        #
        #     # Test basic GPU functionality
        #     import numpy as np
        #     X_test = np.random.random((50, 5)).astype(np.float32)
        #     y_test = np.random.randint(0, 2, 50)
        #
        #     test_model = lgb.LGBMClassifier(
        #         device='gpu',
        #         n_estimators=1,
        #         num_leaves=7,
        #         verbosity=-1
        #     )
        #     test_model.fit(X_test, y_test)
        #     return True
        # except:
        #     return False


class AutoencoderMLP:
    """
    Autoencoder + MLP implementation for Zero-Shot Learning NIDS

    This model combines:
    1. Autoencoder for unsupervised feature learning and dimensionality reduction
    2. MLP classifier for supervised binary classification

    Architecture:
    - Autoencoder: Input -> Encoder -> Latent Space -> Decoder -> Reconstructed Input
    - MLP: Latent Features -> Hidden Layers -> Binary Classification

    This approach is particularly suitable for zero-shot learning as the autoencoder
    learns general data representations that can help detect novel attack patterns.
    """

    def __init__(self, input_size=41, latent_size=20, hidden_size=100, use_gpu=False, fast_mode=False):
        """
        Initialize Autoencoder + MLP model

        Args:
            input_size (int): Number of input features
            latent_size (int): Size of the latent representation
            hidden_size (int): Size of MLP hidden layers
            use_gpu (bool): Whether to use GPU acceleration
            fast_mode (bool): Use reduced parameters for faster training
        """
        if not PYTORCH_AVAILABLE:
            raise ImportError("PyTorch is not installed. Please install with: pip install torch")

        self.input_size = input_size
        self.latent_size = latent_size
        self.hidden_size = hidden_size
        self.use_gpu = use_gpu
        self.fast_mode = fast_mode

        # Set device and check GPU availability
        self.gpu_available = self._check_gpu_availability() if use_gpu else False

        if use_gpu and self.gpu_available:
            self.device = torch.device('cuda')
            print(f"🔥 启用Autoencoder+MLP GPU加速")
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
            print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        else:
            self.device = torch.device('cpu')
            if use_gpu and not self.gpu_available:
                print("⚠️ GPU不可用，使用CPU训练Autoencoder+MLP")

        # Initialize models
        self.autoencoder = None
        self.classifier = None
        self.is_trained = False
        self.training_time = 0
        self.training_history = {
            'autoencoder_loss': [],
            'classifier_loss': [],
            'classifier_accuracy': []
        }

        # Training parameters
        if fast_mode:
            self.ae_epochs = 20
            self.clf_epochs = 10
            self.batch_size = 256
            self.ae_lr = 0.01
            self.clf_lr = 0.01
        else:
            self.ae_epochs = 100
            self.clf_epochs = 50
            self.batch_size = 128
            self.ae_lr = 0.001
            self.clf_lr = 0.001

        print(f"🔧 初始化 Autoencoder+MLP 模型")
        print(f"   计算设备: {'GPU' if self.use_gpu and self.gpu_available else 'CPU'}")
        print(f"   输入维度: {input_size}")
        print(f"   潜在维度: {latent_size}")
        print(f"   隐藏层大小: {hidden_size}")
        print(f"   训练模式: {'快速模式' if fast_mode else '完整模式'}")

    def _create_autoencoder(self):
        """Create the autoencoder network"""
        class Autoencoder(nn.Module):
            def __init__(self, input_size, latent_size):
                super(Autoencoder, self).__init__()

                # Encoder
                self.encoder = nn.Sequential(
                    nn.Linear(input_size, input_size // 2),
                    nn.ReLU(),
                    nn.Linear(input_size // 2, latent_size),
                    nn.ReLU()
                )

                # Decoder
                self.decoder = nn.Sequential(
                    nn.Linear(latent_size, input_size // 2),
                    nn.ReLU(),
                    nn.Linear(input_size // 2, input_size),
                    nn.Sigmoid()  # Assuming normalized input [0,1]
                )

            def forward(self, x):
                encoded = self.encoder(x)
                decoded = self.decoder(encoded)
                return encoded, decoded

            def encode(self, x):
                return self.encoder(x)

        return Autoencoder(self.input_size, self.latent_size).to(self.device)

    def _create_classifier(self):
        """Create the MLP classifier network"""
        class MLPClassifier(nn.Module):
            def __init__(self, latent_size, hidden_size, num_classes=2):
                super(MLPClassifier, self).__init__()

                self.network = nn.Sequential(
                    nn.Linear(latent_size, hidden_size),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(hidden_size, hidden_size),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(hidden_size, num_classes)
                )

            def forward(self, x):
                return self.network(x)

        return MLPClassifier(self.latent_size, self.hidden_size).to(self.device)

    def train(self, X_train, y_train, verbose=True):
        """
        Train the Autoencoder + MLP model

        Args:
            X_train: Training features
            y_train: Training labels
            verbose: Whether to show training progress

        Returns:
            dict: Training results
        """
        if verbose:
            print(f"\n🚀 训练 Autoencoder+MLP 模型...")
            print(f"   训练样本: {X_train.shape[0]:,}")
            print(f"   特征数量: {X_train.shape[1]}")
            print(f"   计算设备: {self.device}")

        start_time = time.time()

        # Convert to PyTorch tensors with proper dtype
        # Handle both DataFrame and numpy array inputs
        if hasattr(X_train, 'values'):  # DataFrame
            X_array = X_train.values
        else:  # numpy array
            X_array = X_train

        if hasattr(y_train, 'values'):  # Series
            y_array = y_train.values
        else:  # numpy array
            y_array = y_train

        # Create tensors on CPU first for DataLoader
        X_tensor = torch.FloatTensor(X_array)
        y_tensor = torch.LongTensor(y_array)

        # Create data loaders (keep data on CPU for DataLoader)
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True, pin_memory=True if self.device.type == 'cuda' else False)

        # Clear GPU cache if using GPU
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()

        # Phase 1: Train Autoencoder (Unsupervised)
        if verbose:
            print(f"\n📊 阶段1: 训练自编码器 (无监督特征学习)")
            print(f"   训练轮数: {self.ae_epochs}")
            print(f"   学习率: {self.ae_lr}")

        self.autoencoder = self._create_autoencoder()
        ae_optimizer = optim.Adam(self.autoencoder.parameters(), lr=self.ae_lr)
        ae_criterion = nn.MSELoss()

        self.autoencoder.train()
        for epoch in range(self.ae_epochs):
            epoch_loss = 0.0
            num_batches = len(dataloader)

            for batch_idx, (batch_X, _) in enumerate(dataloader):
                # Move to device if not already there
                if batch_X.device != self.device:
                    batch_X = batch_X.to(self.device, non_blocking=True)

                ae_optimizer.zero_grad()
                encoded, decoded = self.autoencoder(batch_X)
                loss = ae_criterion(decoded, batch_X)
                loss.backward()
                ae_optimizer.step()
                epoch_loss += loss.item()

                # Clear cache periodically on GPU
                if self.device.type == 'cuda' and batch_idx % 10 == 0:
                    torch.cuda.empty_cache()

            avg_loss = epoch_loss / num_batches
            self.training_history['autoencoder_loss'].append(avg_loss)

            if verbose and (epoch + 1) % max(1, self.ae_epochs // 5) == 0:
                gpu_info = ""
                if self.device.type == 'cuda':
                    allocated = torch.cuda.memory_allocated() / 1e6
                    gpu_info = f" (GPU: {allocated:.0f}MB)"
                print(f"   Epoch {epoch+1}/{self.ae_epochs}: Loss = {avg_loss:.6f}{gpu_info}")

        # Phase 2: Extract features and train classifier
        if verbose:
            print(f"\n🎯 阶段2: 训练分类器 (监督学习)")
            print(f"   训练轮数: {self.clf_epochs}")
            print(f"   学习率: {self.clf_lr}")

        # Extract latent features
        self.autoencoder.eval()
        with torch.no_grad():
            # Move X_tensor to device if not already there
            X_tensor_device = X_tensor.to(self.device)
            latent_features = self.autoencoder.encode(X_tensor_device)

        # Create classifier
        self.classifier = self._create_classifier()
        clf_optimizer = optim.Adam(self.classifier.parameters(), lr=self.clf_lr)
        clf_criterion = nn.CrossEntropyLoss()

        # Create new dataloader with latent features
        latent_dataset = TensorDataset(latent_features, y_tensor)
        latent_dataloader = DataLoader(latent_dataset, batch_size=self.batch_size, shuffle=True)

        self.classifier.train()
        for epoch in range(self.clf_epochs):
            epoch_loss = 0.0
            correct = 0
            total = 0
            num_batches = len(latent_dataloader)

            for batch_idx, (batch_latent, batch_y) in enumerate(latent_dataloader):
                # Move to device if not already there
                if batch_latent.device != self.device:
                    batch_latent = batch_latent.to(self.device, non_blocking=True)
                if batch_y.device != self.device:
                    batch_y = batch_y.to(self.device, non_blocking=True)

                clf_optimizer.zero_grad()
                outputs = self.classifier(batch_latent)
                loss = clf_criterion(outputs, batch_y)
                loss.backward()
                clf_optimizer.step()

                epoch_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += batch_y.size(0)
                correct += (predicted == batch_y).sum().item()

                # Clear cache periodically on GPU
                if self.device.type == 'cuda' and batch_idx % 10 == 0:
                    torch.cuda.empty_cache()

            avg_loss = epoch_loss / num_batches
            accuracy = correct / total

            self.training_history['classifier_loss'].append(avg_loss)
            self.training_history['classifier_accuracy'].append(accuracy)

            if verbose and (epoch + 1) % max(1, self.clf_epochs // 5) == 0:
                gpu_info = ""
                if self.device.type == 'cuda':
                    allocated = torch.cuda.memory_allocated() / 1e6
                    gpu_info = f" (GPU: {allocated:.0f}MB)"
                print(f"   Epoch {epoch+1}/{self.clf_epochs}: Loss = {avg_loss:.6f}, Accuracy = {accuracy:.4f}{gpu_info}")

        self.training_time = time.time() - start_time
        self.is_trained = True

        # Final training accuracy
        self.autoencoder.eval()
        self.classifier.eval()
        with torch.no_grad():
            # Ensure tensors are on the correct device
            X_tensor_device = X_tensor.to(self.device)
            y_tensor_device = y_tensor.to(self.device)
            latent_features = self.autoencoder.encode(X_tensor_device)
            outputs = self.classifier(latent_features)
            _, predicted = torch.max(outputs, 1)
            final_accuracy = (predicted == y_tensor_device).float().mean().item()

        results = {
            'training_time': self.training_time,
            'training_accuracy': final_accuracy,
            'autoencoder_epochs': self.ae_epochs,
            'classifier_epochs': self.clf_epochs,
            'latent_size': self.latent_size,
            'final_ae_loss': self.training_history['autoencoder_loss'][-1],
            'final_clf_loss': self.training_history['classifier_loss'][-1],
            'model_size': self._calculate_model_size()
        }

        if verbose:
            print(f"\n   ✅ 训练完成!")
            print(f"   ⏱️ 训练时间: {self.training_time:.2f}秒")
            print(f"   📊 最终准确率: {final_accuracy:.4f}")
            print(f"   🔄 自编码器损失: {results['final_ae_loss']:.6f}")
            print(f"   🎯 分类器损失: {results['final_clf_loss']:.6f}")
            print(f"   📏 潜在维度: {self.latent_size}")

        return results

    def predict(self, X_test):
        """Make predictions with batch processing for efficiency"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")

        self.autoencoder.eval()
        self.classifier.eval()

        # Handle DataFrame input
        if hasattr(X_test, 'values'):
            X_array = X_test.values
        else:
            X_array = X_test

        # Use batch processing for large datasets
        batch_size = min(1024, len(X_array))
        predictions = []

        with torch.no_grad():
            for i in range(0, len(X_array), batch_size):
                batch_X = X_array[i:i+batch_size]
                X_tensor = torch.FloatTensor(batch_X).to(self.device)
                latent_features = self.autoencoder.encode(X_tensor)
                outputs = self.classifier(latent_features)
                _, predicted = torch.max(outputs, 1)
                predictions.extend(predicted.cpu().numpy())

                # Clear cache for GPU
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

        return np.array(predictions)

    def predict_proba(self, X_test):
        """Get prediction probabilities with batch processing"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")

        self.autoencoder.eval()
        self.classifier.eval()

        # Handle DataFrame input
        if hasattr(X_test, 'values'):
            X_array = X_test.values
        else:
            X_array = X_test

        # Use batch processing for large datasets
        batch_size = min(1024, len(X_array))
        probabilities = []

        with torch.no_grad():
            for i in range(0, len(X_array), batch_size):
                batch_X = X_array[i:i+batch_size]
                X_tensor = torch.FloatTensor(batch_X).to(self.device)
                latent_features = self.autoencoder.encode(X_tensor)
                outputs = self.classifier(latent_features)
                batch_probs = torch.softmax(outputs, dim=1)
                probabilities.extend(batch_probs.cpu().numpy())

                # Clear cache for GPU
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

        return np.array(probabilities)

    def get_latent_features(self, X):
        """Extract latent features using the trained autoencoder"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")

        # Handle DataFrame input
        if hasattr(X, 'values'):
            X_array = X.values
        else:
            X_array = X

        self.autoencoder.eval()
        with torch.no_grad():
            X_tensor = torch.FloatTensor(X_array).to(self.device)
            latent_features = self.autoencoder.encode(X_tensor)
            return latent_features.cpu().numpy()

    def _calculate_model_size(self):
        """Calculate approximate model size in MB"""
        if self.autoencoder is None or self.classifier is None:
            return 0.0

        # Count parameters
        ae_params = sum(p.numel() for p in self.autoencoder.parameters())
        clf_params = sum(p.numel() for p in self.classifier.parameters())
        total_params = ae_params + clf_params

        # Estimate size (4 bytes per float32 parameter)
        size_bytes = total_params * 4
        return size_bytes / (1024 * 1024)  # Convert to MB

    def _check_gpu_availability(self):
        """
        Check if GPU is available for PyTorch

        Returns:
            bool: True if GPU is available, False otherwise
        """
        try:
            if not torch.cuda.is_available():
                return False

            # Test basic GPU functionality
            device = torch.device('cuda')
            test_tensor = torch.randn(10, 10).to(device)
            test_result = test_tensor @ test_tensor.T

            # If we get here, GPU is working
            return True

        except Exception as e:
            return False

    def get_training_history(self):
        """Get training history for visualization"""
        return self.training_history

    def save_model(self, filepath):
        """Save the trained model"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")

        torch.save({
            'autoencoder_state_dict': self.autoencoder.state_dict(),
            'classifier_state_dict': self.classifier.state_dict(),
            'input_size': self.input_size,
            'latent_size': self.latent_size,
            'hidden_size': self.hidden_size,
            'training_history': self.training_history
        }, filepath)

    def load_model(self, filepath):
        """Load a trained model"""
        checkpoint = torch.load(filepath, map_location=self.device)

        self.input_size = checkpoint['input_size']
        self.latent_size = checkpoint['latent_size']
        self.hidden_size = checkpoint['hidden_size']

        # Recreate models
        self.autoencoder = self._create_autoencoder()
        self.classifier = self._create_classifier()

        # Load state dicts
        self.autoencoder.load_state_dict(checkpoint['autoencoder_state_dict'])
        self.classifier.load_state_dict(checkpoint['classifier_state_dict'])

        self.training_history = checkpoint.get('training_history', {})
        self.is_trained = True


def main():
    """
    Test the enhanced model implementations
    """
    print("🧪 Testing Enhanced Model Implementations")
    print("="*60)

    # Generate sample data for testing
    from sklearn.datasets import make_classification

    X, y = make_classification(
        n_samples=10000,
        n_features=41,  # Same as UNSW-NB15 after preprocessing
        n_classes=2,
        random_state=42
    )

    print(f"Test data: {X.shape[0]:,} samples, {X.shape[1]} features")

    # Test Random Forest
    print(f"\n1. Testing Random Forest...")
    rf = EnhancedRandomForest()
    rf_results = rf.train(X, y)

    # Test MLP
    print(f"\n2. Testing MLP...")
    mlp = EnhancedMLP()
    mlp_results = mlp.train(X, y)

    # Test XGBoost (if available)
    xgb_results = None
    if XGBOOST_AVAILABLE:
        print(f"\n3. Testing XGBoost...")
        xgb = EnhancedXGBoost()
        xgb_results = xgb.train(X, y)
    else:
        print(f"\n3. XGBoost skipped (not installed)")

    # Compare models
    compare_models(rf_results, mlp_results, xgb_results)

    print(f"\n✅ Model implementation testing completed!")


class TransformerNIDS:
    """
    Transformer-based Network Intrusion Detection System for Zero-Shot Learning

    This model uses a Transformer encoder architecture to learn complex patterns
    in network traffic data for zero-shot attack detection. The self-attention
    mechanism allows the model to capture long-range dependencies and relationships
    between different features, making it particularly suitable for detecting
    novel attack patterns.

    Architecture:
    - Input Embedding: Linear projection of input features to model dimension
    - Positional Encoding: Learnable positional embeddings for feature positions
    - Transformer Encoder: Multi-head self-attention layers with feed-forward networks
    - Classification Head: Final linear layer for binary classification

    Key advantages for zero-shot learning:
    1. Self-attention captures complex feature interactions
    2. Position-aware processing of network features
    3. Strong representation learning capabilities
    4. Ability to generalize to unseen attack patterns
    """

    def __init__(self, input_size=41, d_model=128, nhead=8, num_layers=4,
                 dim_feedforward=512, dropout=0.1, use_gpu=False, fast_mode=False, high_performance=False):
        """
        Initialize Transformer NIDS model

        Args:
            input_size (int): Number of input features
            d_model (int): Dimension of the model (embedding size)
            nhead (int): Number of attention heads
            num_layers (int): Number of transformer encoder layers
            dim_feedforward (int): Dimension of feedforward network
            dropout (float): Dropout rate
            use_gpu (bool): Whether to use GPU acceleration
            fast_mode (bool): Use reduced parameters for faster training
        """
        if not PYTORCH_AVAILABLE:
            raise ImportError("PyTorch is not installed. Please install with: pip install torch")

        self.input_size = input_size
        self.d_model = d_model
        self.nhead = nhead
        self.num_layers = num_layers
        self.dim_feedforward = dim_feedforward
        self.dropout = dropout
        self.use_gpu = use_gpu
        self.fast_mode = fast_mode
        self.high_performance = high_performance

        # Set device and check GPU availability
        self.gpu_available = self._check_gpu_availability() if use_gpu else False

        if use_gpu and self.gpu_available:
            self.device = torch.device('cuda')
            print(f"🔥 启用Transformer GPU加速")
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
            print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        else:
            self.device = torch.device('cpu')
            if use_gpu and not self.gpu_available:
                print("⚠️ GPU不可用，使用CPU训练Transformer")

        # Adjust parameters for different modes
        if high_performance:
            # 高性能模式：最大化GPU利用率
            self.d_model = d_model  # 保持原始模型大小
            self.nhead = nhead
            self.num_layers = num_layers
            self.dim_feedforward = dim_feedforward
            self.epochs = 50  # 中等轮数
            self.batch_size = 512  # 最优批次大小
            self.learning_rate = 0.002  # 适中学习率
        elif fast_mode:
            self.d_model = 64
            self.nhead = 4
            self.num_layers = 2
            self.dim_feedforward = 256
            self.epochs = 20
            self.batch_size = 512  # 增大批次以更好利用GPU
            self.learning_rate = 0.01
        else:
            self.epochs = 100
            self.batch_size = 256  # 增大批次以更好利用GPU
            self.learning_rate = 0.001

        # Initialize model components
        self.model = None
        self.is_trained = False
        self.training_time = 0
        self.training_history = {
            'loss': [],
            'accuracy': [],
            'attention_weights': []
        }

        print(f"🔧 初始化 Transformer NIDS 模型")
        print(f"   计算设备: {'GPU' if self.use_gpu and self.gpu_available else 'CPU'}")
        print(f"   输入维度: {input_size}")
        print(f"   模型维度: {self.d_model}")
        print(f"   注意力头数: {self.nhead}")
        print(f"   编码器层数: {self.num_layers}")
        print(f"   训练模式: {'快速模式' if fast_mode else '完整模式'}")

    def _create_transformer_model(self):
        """Create the Transformer model with robust architecture"""
        class TransformerClassifier(nn.Module):
            def __init__(self, input_size, d_model, nhead, num_layers, dim_feedforward, dropout):
                super(TransformerClassifier, self).__init__()

                # Ensure d_model is divisible by nhead
                if d_model % nhead != 0:
                    d_model = ((d_model // nhead) + 1) * nhead
                    print(f"⚠️ 调整d_model为{d_model}以确保能被nhead({nhead})整除")

                self.d_model = d_model
                self.input_size = input_size

                # Input projection layer with strong numerical stability
                self.input_projection = nn.Sequential(
                    nn.BatchNorm1d(input_size),  # 输入标准化
                    nn.Linear(input_size, d_model),
                    nn.BatchNorm1d(d_model),     # 投影后标准化
                    nn.GELU(),                   # 更稳定的激活函数
                    nn.Dropout(dropout * 0.3)   # 轻度dropout
                )

                # Positional encoding (fixed sinusoidal encoding for stability)
                self.register_buffer('positional_encoding', self._create_positional_encoding(input_size, d_model))

                # Transformer encoder with proper initialization
                encoder_layer = nn.TransformerEncoderLayer(
                    d_model=d_model,
                    nhead=nhead,
                    dim_feedforward=dim_feedforward,
                    dropout=dropout,
                    activation='gelu',  # GELU is more stable than ReLU
                    batch_first=True,
                    norm_first=True  # Pre-norm for better stability
                )
                self.transformer_encoder = nn.TransformerEncoder(
                    encoder_layer,
                    num_layers=num_layers
                )

                # Classification head with strong regularization
                self.classifier = nn.Sequential(
                    nn.LayerNorm(d_model),
                    nn.Linear(d_model, d_model // 2),
                    nn.BatchNorm1d(d_model // 2),  # 批次归一化
                    nn.GELU(),
                    nn.Dropout(dropout),
                    nn.Linear(d_model // 2, d_model // 4),
                    nn.BatchNorm1d(d_model // 4),  # 批次归一化
                    nn.GELU(),
                    nn.Dropout(dropout * 0.5),
                    nn.Linear(d_model // 4, 2)    # Binary classification
                )

                # 输出稳定化层
                self.output_stabilizer = nn.Sequential(
                    nn.Tanh(),  # 限制输出范围到[-1, 1]
                    nn.Linear(2, 2)  # 最终线性变换
                )

                # Initialize weights properly
                self._init_weights()

            def _create_positional_encoding(self, seq_len, d_model):
                """Create fixed sinusoidal positional encoding"""
                pe = torch.zeros(seq_len, d_model)
                position = torch.arange(0, seq_len, dtype=torch.float).unsqueeze(1)
                div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                                   (-np.log(10000.0) / d_model))
                pe[:, 0::2] = torch.sin(position * div_term)
                pe[:, 1::2] = torch.cos(position * div_term)
                return pe.unsqueeze(0)  # (1, seq_len, d_model)

            def _init_weights(self):
                """Initialize weights with conservative initialization for numerical stability"""
                for module in self.modules():
                    if isinstance(module, nn.Linear):
                        # 使用更保守的初始化
                        nn.init.xavier_normal_(module.weight, gain=0.5)  # 减小初始权重
                        if module.bias is not None:
                            nn.init.constant_(module.bias, 0)
                    elif isinstance(module, (nn.LayerNorm, nn.BatchNorm1d)):
                        if hasattr(module, 'bias') and module.bias is not None:
                            nn.init.constant_(module.bias, 0)
                        if hasattr(module, 'weight') and module.weight is not None:
                            nn.init.constant_(module.weight, 1.0)
                    elif isinstance(module, nn.TransformerEncoderLayer):
                        # 特别初始化Transformer层
                        for param in module.parameters():
                            if param.dim() > 1:
                                nn.init.xavier_normal_(param, gain=0.3)  # 更保守的初始化

            def forward(self, x):
                # x shape: (batch_size, input_size)
                batch_size, seq_len = x.size()

                # 强制数值稳定性 - 输入预处理
                x = self._stabilize_input(x)

                # Project input to d_model dimension
                # (batch_size, input_size) -> (batch_size, d_model)
                x_proj = self.input_projection(x)  # (batch_size, d_model)

                # 中间层稳定性检查
                x_proj = self._stabilize_tensor(x_proj, "input_projection")

                # Reshape for transformer: treat as sequence of length 1
                # (batch_size, d_model) -> (batch_size, 1, d_model)
                x_seq = x_proj.unsqueeze(1)

                # Add positional encoding (only first position since seq_len=1)
                pos_enc = self.positional_encoding[:, :1, :]  # (1, 1, d_model)
                x_seq = x_seq + pos_enc

                # 位置编码后稳定性检查
                x_seq = self._stabilize_tensor(x_seq, "positional_encoding")

                # Apply transformer encoder
                transformer_output = self.transformer_encoder(x_seq)  # (batch_size, 1, d_model)

                # Transformer输出稳定性检查
                transformer_output = self._stabilize_tensor(transformer_output, "transformer_output")

                # Remove sequence dimension
                pooled_output = transformer_output.squeeze(1)  # (batch_size, d_model)

                # Classification
                logits = self.classifier(pooled_output)

                # 最终输出稳定化
                logits = self._stabilize_tensor(logits, "classifier_output")

                # 应用输出稳定化层
                stable_logits = self.output_stabilizer(logits)

                return stable_logits

            def _stabilize_input(self, x, clip_value=10.0):
                """稳定化输入数据"""
                # 替换NaN和Inf
                x = torch.where(torch.isnan(x) | torch.isinf(x), torch.zeros_like(x), x)

                # 裁剪极值
                x = torch.clamp(x, -clip_value, clip_value)

                # 标准化到合理范围
                x_std = torch.std(x, dim=1, keepdim=True) + 1e-8
                x_mean = torch.mean(x, dim=1, keepdim=True)
                x = (x - x_mean) / x_std

                # 再次裁剪确保安全
                x = torch.clamp(x, -3.0, 3.0)

                return x

            def _stabilize_tensor(self, tensor, stage_name="unknown", clip_value=100.0):
                """通用张量稳定化函数"""
                # 检查并替换NaN/Inf
                if torch.isnan(tensor).any() or torch.isinf(tensor).any():
                    print(f"⚠️ 在{stage_name}阶段检测到NaN/Inf，进行修复")
                    tensor = torch.where(torch.isnan(tensor) | torch.isinf(tensor),
                                       torch.zeros_like(tensor), tensor)

                # 裁剪极值
                tensor = torch.clamp(tensor, -clip_value, clip_value)

                # 检查梯度范数
                if tensor.requires_grad and tensor.grad is not None:
                    grad_norm = torch.norm(tensor.grad)
                    if grad_norm > 1000:
                        print(f"⚠️ 在{stage_name}阶段检测到大梯度: {grad_norm:.2f}")

                return tensor

            def get_attention_weights(self, x):
                """Extract attention weights for visualization"""
                with torch.no_grad():
                    self.eval()
                    _ = self.forward(x)
                    # Return dummy attention weights for now
                    return torch.ones(x.size(0), self.nhead, 1, 1)

        return TransformerClassifier(
            self.input_size, self.d_model, self.nhead,
            self.num_layers, self.dim_feedforward, self.dropout
        ).to(self.device)

    def train(self, X_train, y_train, verbose=True):
        """
        Train the Transformer NIDS model

        Args:
            X_train: Training features
            y_train: Training labels
            verbose: Whether to show training progress

        Returns:
            dict: Training results
        """
        if verbose:
            print(f"\n🚀 训练 Transformer NIDS 模型...")
            print(f"   训练样本: {X_train.shape[0]:,}")
            print(f"   特征数量: {X_train.shape[1]}")
            print(f"   计算设备: {self.device}")
            print(f"   模型参数: d_model={self.d_model}, heads={self.nhead}, layers={self.num_layers}")

        start_time = time.time()

        # Convert to PyTorch tensors
        if hasattr(X_train, 'values'):  # DataFrame
            X_array = X_train.values
        else:  # numpy array
            X_array = X_train

        if hasattr(y_train, 'values'):  # Series
            y_array = y_train.values
        else:  # numpy array
            y_array = y_train

        # Create tensors on CPU first for DataLoader
        X_tensor = torch.FloatTensor(X_array)
        y_tensor = torch.LongTensor(y_array)

        # Create data loader
        from torch.utils.data import TensorDataset, DataLoader
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(
            dataset,
            batch_size=self.batch_size,
            shuffle=True,
            pin_memory=True if self.device.type == 'cuda' else False
        )

        # Clear GPU cache if using GPU
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()

        # Create model
        self.model = self._create_transformer_model()

        # Setup optimizer with ultra-conservative settings for numerical stability
        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=min(self.learning_rate, 0.001),  # 限制最大学习率
            weight_decay=1e-3,  # 更强的正则化
            eps=1e-8,
            betas=(0.9, 0.999),
            amsgrad=True
        )

        # 损失函数配置
        criterion = nn.CrossEntropyLoss(
            label_smoothing=0.15,  # 更强的标签平滑
            reduction='mean'
        )

        # 学习率调度器 - 更保守的设置
        scheduler = torch.optim.lr_scheduler.ExponentialLR(
            optimizer, gamma=0.95  # 每轮衰减5%
        )

        # 梯度缩放器（用于混合精度训练）
        scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None

        # Training loop
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        patience = 15

        if verbose:
            print(f"\n📊 开始训练 ({self.epochs} 轮)...")

        for epoch in range(self.epochs):
            epoch_loss = 0.0
            correct_predictions = 0
            total_samples = 0

            # Progress tracking
            if verbose and epoch % 10 == 0:
                print(f"\n--- 轮次 {epoch+1}/{self.epochs} ---")

            for batch_idx, (batch_X, batch_y) in enumerate(dataloader):
                # Move to device with proper dtype
                batch_X = batch_X.to(self.device, dtype=torch.float32, non_blocking=True)
                batch_y = batch_y.to(self.device, dtype=torch.long, non_blocking=True)

                # 强制数据预处理和稳定化
                batch_X = self._preprocess_batch(batch_X)

                # Forward pass
                optimizer.zero_grad()

                try:
                    # Use autocast for mixed precision (more stable)
                    with torch.cuda.amp.autocast(enabled=self.device.type == 'cuda'):
                        outputs = self.model(batch_X)

                        # 验证输出
                        if not torch.isfinite(outputs).all():
                            print(f"⚠️ 模型输出包含无限值，使用安全输出")
                            outputs = torch.zeros_like(outputs)

                        loss = criterion(outputs, batch_y)

                        # 验证损失
                        if not torch.isfinite(loss):
                            print(f"⚠️ 损失不是有限值，使用默认损失")
                            loss = torch.tensor(1.0, device=self.device, requires_grad=True)

                    # Backward pass with enhanced stability
                    if scaler is not None:
                        # GPU with mixed precision
                        scaler.scale(loss).backward()

                        # 检查梯度并进行稳定化
                        self._stabilize_gradients()

                        # Unscale gradients for clipping
                        scaler.unscale_(optimizer)

                        # 保守的梯度裁剪
                        grad_norm = torch.nn.utils.clip_grad_norm_(
                            self.model.parameters(), max_norm=0.5  # 更保守的裁剪
                        )

                        # 检查梯度范数
                        if grad_norm > 10.0:
                            print(f"⚠️ 大梯度范数: {grad_norm:.2f}，重置梯度")
                            optimizer.zero_grad()
                            continue

                        # 只有在梯度完全有限时才更新
                        if self._check_gradients_finite():
                            scaler.step(optimizer)
                            scaler.update()
                        else:
                            print(f"⚠️ 检测到无限梯度，重置并继续")
                            optimizer.zero_grad()

                    else:
                        # CPU training
                        loss.backward()

                        # 稳定化梯度
                        self._stabilize_gradients()

                        # 梯度裁剪
                        grad_norm = torch.nn.utils.clip_grad_norm_(
                            self.model.parameters(), max_norm=0.5
                        )

                        if grad_norm > 10.0 or not self._check_gradients_finite():
                            print(f"⚠️ 梯度问题，重置并继续")
                            optimizer.zero_grad()
                            continue

                        optimizer.step()

                except Exception as e:
                    print(f"⚠️ 训练步骤异常: {e}，重置并继续")
                    optimizer.zero_grad()
                    continue

                # Statistics
                epoch_loss += loss.item()
                _, predicted = torch.max(outputs, 1)
                correct_predictions += (predicted == batch_y).sum().item()
                total_samples += batch_y.size(0)

                # Clear cache periodically on GPU
                if self.device.type == 'cuda' and batch_idx % 10 == 0:
                    torch.cuda.empty_cache()

            # Calculate epoch metrics
            avg_loss = epoch_loss / len(dataloader) if len(dataloader) > 0 else float('inf')
            accuracy = correct_predictions / total_samples if total_samples > 0 else 0.0

            # Check for NaN in metrics
            if np.isnan(avg_loss) or np.isinf(avg_loss):
                print(f"⚠️ 警告: 轮次 {epoch+1} 平均损失异常: {avg_loss}")
                avg_loss = float('inf')

            if np.isnan(accuracy):
                print(f"⚠️ 警告: 轮次 {epoch+1} 准确率异常: {accuracy}")
                accuracy = 0.0

            # Store training history
            self.training_history['loss'].append(avg_loss)
            self.training_history['accuracy'].append(accuracy)

            # Learning rate scheduling
            if not (np.isnan(avg_loss) or np.isinf(avg_loss)):
                scheduler.step()  # CosineAnnealingWarmRestarts doesn't need loss

            # Progress display
            if verbose and (epoch % 10 == 0 or epoch < 5 or epoch >= self.epochs - 5):
                print(f"   轮次 {epoch+1:3d}: 损失={avg_loss:.6f}, 准确率={accuracy:.4f}")

            # Early stopping
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= patience:
                if verbose:
                    print(f"\n  🛑 早停: 连续{patience}轮无改进")
                break

        # Training completed
        self.training_time = time.time() - start_time
        self.is_trained = True

        # Final accuracy calculation
        final_accuracy = self.training_history['accuracy'][-1] if self.training_history['accuracy'] else 0.0

        results = {
            'training_time': self.training_time,
            'training_accuracy': final_accuracy,
            'epochs_completed': len(self.training_history['loss']),
            'final_loss': self.training_history['loss'][-1] if self.training_history['loss'] else 0.0,
            'model_parameters': sum(p.numel() for p in self.model.parameters()),
            'model_size_mb': self._calculate_model_size()
        }

        if verbose:
            print(f"\n   ✅ 训练完成!")
            print(f"   ⏱️ 训练时间: {self.training_time:.2f}秒")
            print(f"   📊 最终准确率: {final_accuracy:.4f}")
            print(f"   🔄 完成轮数: {results['epochs_completed']}")
            print(f"   📏 模型参数: {results['model_parameters']:,}")
            print(f"   💾 模型大小: {results['model_size_mb']:.2f} MB")

        return results

    def _preprocess_batch(self, batch_X):
        """预处理批次数据确保数值稳定性"""
        # 替换NaN和Inf
        batch_X = torch.where(torch.isnan(batch_X) | torch.isinf(batch_X),
                             torch.zeros_like(batch_X), batch_X)

        # 裁剪极值
        batch_X = torch.clamp(batch_X, -10.0, 10.0)

        # 批次级别的标准化
        batch_mean = torch.mean(batch_X, dim=1, keepdim=True)
        batch_std = torch.std(batch_X, dim=1, keepdim=True) + 1e-8
        batch_X = (batch_X - batch_mean) / batch_std

        # 最终裁剪
        batch_X = torch.clamp(batch_X, -5.0, 5.0)

        return batch_X

    def _stabilize_gradients(self):
        """稳定化所有参数的梯度"""
        for param in self.model.parameters():
            if param.grad is not None:
                # 替换NaN和Inf梯度
                param.grad = torch.where(
                    torch.isnan(param.grad) | torch.isinf(param.grad),
                    torch.zeros_like(param.grad),
                    param.grad
                )

                # 裁剪极值梯度
                param.grad = torch.clamp(param.grad, -1.0, 1.0)

    def _check_gradients_finite(self):
        """检查所有梯度是否有限"""
        for param in self.model.parameters():
            if param.grad is not None:
                if not torch.isfinite(param.grad).all():
                    return False
        return True

    def predict(self, X_test):
        """Make predictions with batch processing and NaN handling"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")

        self.model.eval()

        # Handle DataFrame input
        if hasattr(X_test, 'values'):
            X_array = X_test.values
        else:
            X_array = X_test

        # Use batch processing for large datasets
        batch_size = min(1024, len(X_array))
        predictions = []

        with torch.no_grad():
            for i in range(0, len(X_array), batch_size):
                batch_X = X_array[i:i+batch_size]
                X_tensor = torch.FloatTensor(batch_X).to(self.device)

                try:
                    outputs = self.model(X_tensor)

                    # Check for NaN in outputs
                    if torch.isnan(outputs).any():
                        print(f"⚠️ 警告: 预测输出包含NaN，使用默认预测")
                        # Use default prediction (class 1 for attack detection)
                        batch_predictions = np.ones(batch_X.shape[0], dtype=int)
                    else:
                        _, predicted = torch.max(outputs, 1)
                        batch_predictions = predicted.cpu().numpy()

                    predictions.extend(batch_predictions)

                except Exception as e:
                    print(f"⚠️ 警告: 预测批次失败: {e}，使用默认预测")
                    # Use default prediction for failed batches
                    batch_predictions = np.ones(batch_X.shape[0], dtype=int)
                    predictions.extend(batch_predictions)

                # Clear cache for GPU
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

        return np.array(predictions)

    def predict_proba(self, X_test):
        """Get prediction probabilities with batch processing and NaN handling"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")

        self.model.eval()

        # Handle DataFrame input
        if hasattr(X_test, 'values'):
            X_array = X_test.values
        else:
            X_array = X_test

        # Use batch processing for large datasets
        batch_size = min(1024, len(X_array))
        probabilities = []

        with torch.no_grad():
            for i in range(0, len(X_array), batch_size):
                batch_X = X_array[i:i+batch_size]
                X_tensor = torch.FloatTensor(batch_X).to(self.device)

                try:
                    outputs = self.model(X_tensor)

                    # Check for NaN in outputs
                    if torch.isnan(outputs).any():
                        print(f"⚠️ 警告: 预测输出包含NaN，使用默认概率")
                        # Use default probabilities [0.5, 0.5] for NaN cases
                        batch_probs = np.full((batch_X.shape[0], 2), 0.5)
                    else:
                        probs = torch.softmax(outputs, dim=1)
                        batch_probs = probs.cpu().numpy()

                        # Double-check for NaN in probabilities
                        if np.isnan(batch_probs).any():
                            print(f"⚠️ 警告: 概率计算包含NaN，使用默认概率")
                            batch_probs = np.full((batch_X.shape[0], 2), 0.5)

                    probabilities.extend(batch_probs)

                except Exception as e:
                    print(f"⚠️ 警告: 预测批次失败: {e}，使用默认概率")
                    # Use default probabilities for failed batches
                    batch_probs = np.full((batch_X.shape[0], 2), 0.5)
                    probabilities.extend(batch_probs)

                # Clear cache for GPU
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

        result = np.array(probabilities)

        # Final check for NaN in result
        if np.isnan(result).any():
            print(f"⚠️ 警告: 最终概率结果包含NaN，替换为默认值")
            result = np.where(np.isnan(result), 0.5, result)

        return result

    def get_attention_weights(self, X_sample):
        """Extract attention weights for a sample (for visualization)"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")

        self.model.eval()

        # Handle DataFrame input
        if hasattr(X_sample, 'values'):
            X_array = X_sample.values
        else:
            X_array = X_sample

        # Take only first sample if multiple provided
        if len(X_array.shape) > 1 and X_array.shape[0] > 1:
            X_array = X_array[:1]

        with torch.no_grad():
            X_tensor = torch.FloatTensor(X_array).to(self.device)
            attention_weights = self.model.get_attention_weights(X_tensor)
            return attention_weights.cpu().numpy()

    def _calculate_model_size(self):
        """Calculate approximate model size in MB"""
        if self.model is None:
            return 0.0

        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())

        # Estimate size (4 bytes per float32 parameter)
        size_bytes = total_params * 4
        return size_bytes / (1024 * 1024)  # Convert to MB

    def _check_gpu_availability(self):
        """
        Check if GPU is available for PyTorch

        Returns:
            bool: True if GPU is available, False otherwise
        """
        try:
            if not torch.cuda.is_available():
                return False

            # Test basic GPU functionality
            device = torch.device('cuda')
            test_tensor = torch.randn(10, 10).to(device)
            test_result = test_tensor @ test_tensor.T

            # If we get here, GPU is working
            return True

        except Exception as e:
            return False

    def get_training_history(self):
        """Get training history for visualization"""
        return self.training_history

    def save_model(self, filepath):
        """Save the trained model"""
        if not self.is_trained:
            raise ValueError("Model not trained yet. Call train() first.")

        torch.save({
            'model_state_dict': self.model.state_dict(),
            'input_size': self.input_size,
            'd_model': self.d_model,
            'nhead': self.nhead,
            'num_layers': self.num_layers,
            'dim_feedforward': self.dim_feedforward,
            'dropout': self.dropout,
            'training_history': self.training_history
        }, filepath)

    def load_model(self, filepath):
        """Load a trained model"""
        checkpoint = torch.load(filepath, map_location=self.device)

        self.input_size = checkpoint['input_size']
        self.d_model = checkpoint['d_model']
        self.nhead = checkpoint['nhead']
        self.num_layers = checkpoint['num_layers']
        self.dim_feedforward = checkpoint['dim_feedforward']
        self.dropout = checkpoint['dropout']

        # Recreate model
        self.model = self._create_transformer_model()

        # Load state dict
        self.model.load_state_dict(checkpoint['model_state_dict'])

        self.training_history = checkpoint.get('training_history', {})
        self.is_trained = True


if __name__ == '__main__':
    main()
