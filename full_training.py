"""
完整数据集GPU训练程序
Full Dataset GPU Training for Zero-Shot Learning NIDS

使用完整UNSW-NB15数据集和GPU加速进行论文标准的完整训练
Based on: "From Zero-Shot Machine Learning to Zero-Day Attack Detection"
"""

import os
import sys
import time
import argparse
import numpy as np
import pandas as pd
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

# Import our modules
from data_preprocessing import DataPreprocessor
from gpu_models import GPUMLPTrainer, FullDatasetRandomForest, check_gpu_availability
from evaluation import ZeroShotEvaluator
from visualization import ZeroShotVisualizer


class FullTrainingManager:
    """
    管理完整数据集的GPU训练流程
    """
    
    def __init__(self, output_dir='results_full'):
        """
        初始化完整训练管理器
        
        Args:
            output_dir: 结果输出目录
        """
        self.output_dir = output_dir
        self.results_dir = os.path.join(output_dir, 'results')
        self.figures_dir = os.path.join(output_dir, 'figures')
        self.reports_dir = os.path.join(output_dir, 'reports')
        
        # 创建输出目录
        for dir_path in [self.output_dir, self.results_dir, self.figures_dir, self.reports_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 初始化组件
        self.preprocessor = DataPreprocessor()
        self.evaluator = ZeroShotEvaluator(self.figures_dir)
        self.visualizer = ZeroShotVisualizer(self.figures_dir)
        
        # 训练结果存储
        self.training_results = {}
        
    def load_full_dataset(self, train_path, test_path):
        """
        加载完整UNSW-NB15数据集
        
        Args:
            train_path: 训练集路径
            test_path: 测试集路径
            
        Returns:
            pandas.DataFrame: 完整数据集
        """
        print(f"\n📊 加载完整UNSW-NB15数据集")
        print("="*60)
        
        # 加载数据
        data = self.preprocessor.load_data(train_path, test_path)
        
        print(f"✅ 数据加载完成")
        print(f"   总样本数: {len(data):,}")
        print(f"   特征数: {data.shape[1]}")
        
        # 数据集统计
        print(f"\n📈 数据集统计:")
        attack_counts = data['attack_cat'].value_counts()
        for attack, count in attack_counts.items():
            percentage = count / len(data) * 100
            print(f"   {attack}: {count:,} ({percentage:.2f}%)")
        
        return data
    
    def run_full_zero_shot_experiment(self, data, zero_day_attack, model_type, 
                                    epochs=50, batch_size=2048, use_gpu=True):
        """
        运行完整数据集的零样本学习实验
        
        Args:
            data: 完整数据集
            zero_day_attack: 零日攻击类型
            model_type: 模型类型 ('mlp' 或 'rf')
            epochs: 训练轮数 (仅MLP)
            batch_size: 批次大小 (仅MLP)
            use_gpu: 是否使用GPU (仅MLP)
            
        Returns:
            dict: 实验结果
        """
        print(f"\n🎯 完整零样本学习实验: {zero_day_attack}")
        print("="*70)
        print(f"模型类型: {model_type.upper()}")
        print(f"零日攻击: {zero_day_attack}")
        print(f"数据集大小: {len(data):,}")
        
        # 数据预处理
        print(f"\n🔄 数据预处理...")
        preprocessing_results = self.preprocessor.fit_transform(
            data, zero_day_attack, fast_mode=False, sample_size=None
        )
        
        if 'zsl_data' not in preprocessing_results:
            raise ValueError(f"无法为 {zero_day_attack} 生成零样本学习数据")
        
        X_train, y_train_attack, y_train_binary, X_test, y_test_attack, y_test_binary, zero_day_mask = preprocessing_results['zsl_data']
        
        print(f"✅ 预处理完成")
        print(f"   训练集: {X_train.shape[0]:,} 样本")
        print(f"   测试集: {X_test.shape[0]:,} 样本")
        print(f"   零日样本: {zero_day_mask.sum():,} 样本")
        print(f"   特征数: {X_train.shape[1]}")
        
        # 模型训练
        start_time = time.time()
        
        if model_type == 'mlp':
            print(f"\n🧠 GPU加速MLP训练 (完整数据集)")
            
            # 验证集分割
            X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
                X_train, y_train_binary, test_size=0.1, random_state=42, stratify=y_train_binary
            )
            
            # 初始化GPU MLP
            device = 'cuda' if use_gpu and check_gpu_availability() else 'cpu'
            model = GPUMLPTrainer(
                input_size=X_train.shape[1],
                hidden_size=100,
                num_classes=2,
                learning_rate=0.001,
                weight_decay=0.0001,
                device=device
            )
            
            # 训练模型
            training_stats = model.train(
                X_train_split, y_train_split,
                X_val_split, y_val_split,
                epochs=epochs,
                batch_size=batch_size,
                patience=10,
                verbose=True
            )
            
        elif model_type == 'rf':
            print(f"\n🌲 完整数据集随机森林训练")
            
            # 初始化随机森林
            model = FullDatasetRandomForest(
                n_estimators=50,
                random_state=42,
                n_jobs=-1
            )
            
            # 训练模型
            training_stats = model.train(X_train, y_train_binary, verbose=True)
            
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        training_time = time.time() - start_time
        
        # 模型预测
        print(f"\n🔮 模型预测...")
        y_pred_binary = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)
        
        # 评估指标计算
        print(f"\n📊 评估指标计算...")
        
        # 整体性能指标
        overall_accuracy = accuracy_score(y_test_binary, y_pred_binary)
        
        # 零日检测率 (Z-DR)
        zero_day_predictions = y_pred_binary[zero_day_mask]
        zero_day_detected = (zero_day_predictions == 1).sum()
        zero_day_total = zero_day_mask.sum()
        z_dr = (zero_day_detected / zero_day_total * 100) if zero_day_total > 0 else 0
        
        # 误报率 (FAR)
        normal_mask = y_test_binary == 0
        normal_predictions = y_pred_binary[normal_mask]
        false_alarms = (normal_predictions == 1).sum()
        total_normal = normal_mask.sum()
        far = (false_alarms / total_normal * 100) if total_normal > 0 else 0
        
        # 详细分类报告
        class_report = classification_report(
            y_test_binary, y_pred_binary, 
            target_names=['Normal', 'Attack'],
            output_dict=True
        )
        
        # 混淆矩阵
        conf_matrix = confusion_matrix(y_test_binary, y_pred_binary)
        
        # 结果汇总
        results = {
            'experiment_info': {
                'zero_day_attack': zero_day_attack,
                'model_type': model_type,
                'timestamp': datetime.now().isoformat(),
                'total_samples': len(data),
                'training_samples': X_train.shape[0],
                'test_samples': X_test.shape[0],
                'zero_day_samples': zero_day_total,
                'features': X_train.shape[1]
            },
            'training': {
                'training_time': training_time,
                'training_stats': training_stats,
                'epochs': epochs if model_type == 'mlp' else None,
                'batch_size': batch_size if model_type == 'mlp' else None
            },
            'evaluation': {
                'zero_day_detection_rate': z_dr,
                'false_alarm_rate': far,
                'overall_accuracy': overall_accuracy,
                'classification_report': class_report,
                'confusion_matrix': conf_matrix.tolist(),
                'zero_day_detected': int(zero_day_detected),
                'zero_day_total': int(zero_day_total),
                'false_alarms': int(false_alarms),
                'total_normal': int(total_normal)
            }
        }
        
        # 打印结果
        print(f"\n🎉 实验完成!")
        print(f"   训练时间: {training_time:.2f}秒")
        print(f"   整体准确率: {overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)")
        print(f"   零日检测率 (Z-DR): {z_dr:.2f}%")
        print(f"   误报率 (FAR): {far:.2f}%")
        print(f"   零日样本检测: {zero_day_detected}/{zero_day_total}")
        
        return results
    
    def run_complete_full_experiments(self, train_path, test_path, 
                                    model_types=['mlp', 'rf'],
                                    attack_types=None,
                                    epochs=50, batch_size=2048, use_gpu=True):
        """
        运行完整的全数据集实验套件
        
        Args:
            train_path: 训练集路径
            test_path: 测试集路径
            model_types: 模型类型列表
            attack_types: 攻击类型列表 (None表示全部)
            epochs: MLP训练轮数
            batch_size: MLP批次大小
            use_gpu: 是否使用GPU
            
        Returns:
            dict: 完整实验结果
        """
        print(f"\n🚀 完整数据集实验套件")
        print("="*80)
        print(f"模型: {', '.join([m.upper() for m in model_types])}")
        print(f"MLP训练轮数: {epochs}")
        print(f"批次大小: {batch_size}")
        print(f"GPU加速: {'启用' if use_gpu else '禁用'}")
        
        # 加载完整数据集
        data = self.load_full_dataset(train_path, test_path)
        
        # 攻击类型
        if attack_types is None:
            attack_types = ['Exploits', 'Fuzzers', 'DoS', 'Reconnaissance', 
                          'Analysis', 'Backdoor', 'Shellcode', 'Worms', 'Generic']
        
        print(f"攻击类型: {len(attack_types)} 种")
        
        # 实验结果存储
        all_results = {
            'experiment_info': {
                'timestamp': datetime.now().isoformat(),
                'model_types': model_types,
                'attack_types': attack_types,
                'total_samples': len(data),
                'epochs': epochs,
                'batch_size': batch_size,
                'use_gpu': use_gpu
            },
            'results': {}
        }
        
        # 运行实验
        total_experiments = len(model_types) * len(attack_types)
        experiment_count = 0
        
        for model_type in model_types:
            all_results['results'][model_type] = {}
            
            print(f"\n{'='*20} {model_type.upper()} 模型实验 {'='*20}")
            
            for attack_type in attack_types:
                experiment_count += 1
                print(f"\n进度: {experiment_count}/{total_experiments}")
                
                try:
                    result = self.run_full_zero_shot_experiment(
                        data, attack_type, model_type, 
                        epochs, batch_size, use_gpu
                    )
                    all_results['results'][model_type][attack_type] = result
                    
                    # 保存中间结果
                    self._save_intermediate_results(all_results, model_type, attack_type)
                    
                except Exception as e:
                    print(f"❌ 实验失败 {model_type}-{attack_type}: {e}")
                    all_results['results'][model_type][attack_type] = {
                        'error': str(e),
                        'status': 'failed'
                    }
        
        # 生成最终报告
        self._generate_full_training_report(all_results)
        
        return all_results
    
    def _save_intermediate_results(self, all_results, model_type, attack_type):
        """保存中间结果"""
        import json
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"full_training_results_{timestamp}.json"
        filepath = os.path.join(self.results_dir, filename)
        
        # 转换numpy类型
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return obj
        
        def deep_convert(data):
            if isinstance(data, dict):
                return {k: deep_convert(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [deep_convert(item) for item in data]
            else:
                return convert_numpy(data)
        
        converted_results = deep_convert(all_results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(converted_results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 中间结果已保存: {filepath}")
    
    def _generate_full_training_report(self, all_results):
        """生成完整训练报告"""
        print(f"\n📋 生成完整训练报告...")
        
        report_lines = []
        report_lines.append("="*100)
        report_lines.append("完整数据集GPU训练实验报告")
        report_lines.append("Full Dataset GPU Training Experiment Report")
        report_lines.append("="*100)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 实验配置
        exp_info = all_results['experiment_info']
        report_lines.append("实验配置")
        report_lines.append("-" * 50)
        report_lines.append(f"总样本数: {exp_info['total_samples']:,}")
        report_lines.append(f"模型类型: {', '.join(exp_info['model_types'])}")
        report_lines.append(f"攻击类型数: {len(exp_info['attack_types'])}")
        report_lines.append(f"MLP训练轮数: {exp_info['epochs']}")
        report_lines.append(f"批次大小: {exp_info['batch_size']}")
        report_lines.append(f"GPU加速: {'启用' if exp_info['use_gpu'] else '禁用'}")
        report_lines.append("")
        
        # 性能汇总
        report_lines.append("性能汇总")
        report_lines.append("-" * 50)
        
        for model_type in exp_info['model_types']:
            if model_type in all_results['results']:
                model_results = all_results['results'][model_type]
                successful_results = {k: v for k, v in model_results.items() 
                                    if 'error' not in v and 'evaluation' in v}
                
                if successful_results:
                    z_dr_scores = [r['evaluation']['zero_day_detection_rate'] 
                                 for r in successful_results.values()]
                    far_scores = [r['evaluation']['false_alarm_rate'] 
                                for r in successful_results.values()]
                    
                    avg_z_dr = np.mean(z_dr_scores)
                    avg_far = np.mean(far_scores)
                    
                    report_lines.append(f"\n{model_type.upper()} 模型:")
                    report_lines.append(f"  成功实验: {len(successful_results)}/{len(model_results)}")
                    report_lines.append(f"  平均 Z-DR: {avg_z_dr:.2f}%")
                    report_lines.append(f"  平均 FAR: {avg_far:.2f}%")
                    report_lines.append(f"  Z-DR 范围: [{min(z_dr_scores):.2f}%, {max(z_dr_scores):.2f}%]")
        
        # 保存报告
        report_path = os.path.join(self.reports_dir, 'full_training_report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"✅ 完整训练报告已保存: {report_path}")


def main():
    """
    主函数：运行完整数据集GPU训练
    """
    parser = argparse.ArgumentParser(description='完整数据集GPU训练程序')
    parser.add_argument('--models', default='mlp,rf', help='模型类型 (mlp,rf)')
    parser.add_argument('--epochs', type=int, default=50, help='MLP训练轮数')
    parser.add_argument('--batch-size', type=int, default=2048, help='批次大小')
    parser.add_argument('--no-gpu', action='store_true', help='禁用GPU加速')
    parser.add_argument('--output-dir', default='results_full', help='输出目录')
    parser.add_argument('--attacks', default=None, help='攻击类型 (逗号分隔)')
    
    args = parser.parse_args()
    
    # 解析参数
    model_types = [m.strip() for m in args.models.split(',')]
    use_gpu = not args.no_gpu
    
    attack_types = None
    if args.attacks:
        attack_types = [a.strip() for a in args.attacks.split(',')]
    
    # 检查GPU可用性
    if use_gpu:
        gpu_available = check_gpu_availability()
        if not gpu_available:
            print("⚠️ GPU不可用，将使用CPU训练")
            use_gpu = False
    
    # 数据路径
    train_path = 'data/UNSW/UNSW_NB15_training-set.csv'
    test_path = 'data/UNSW/UNSW_NB15_testing-set.csv'
    
    # 检查数据文件
    for path in [train_path, test_path]:
        if not os.path.exists(path):
            print(f"❌ 数据文件不存在: {path}")
            sys.exit(1)
    
    print(f"🚀 完整数据集GPU训练程序启动")
    print(f"模型: {', '.join(model_types)}")
    print(f"训练轮数: {args.epochs}")
    print(f"批次大小: {args.batch_size}")
    print(f"GPU加速: {'启用' if use_gpu else '禁用'}")
    
    # 初始化训练管理器
    trainer = FullTrainingManager(args.output_dir)
    
    # 运行完整实验
    try:
        results = trainer.run_complete_full_experiments(
            train_path, test_path,
            model_types=model_types,
            attack_types=attack_types,
            epochs=args.epochs,
            batch_size=args.batch_size,
            use_gpu=use_gpu
        )
        
        print(f"\n🎉 完整数据集训练完成!")
        print(f"📁 结果保存在: {args.output_dir}/")
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
