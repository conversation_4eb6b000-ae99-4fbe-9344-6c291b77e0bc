====================================================================================================
零样本学习框架 零日攻击检测 综合实验报告
From Zero-Shot Machine Learning to Zero-Day Attack Detection
====================================================================================================
生成时间: 2025-06-13 06:51:40

执行摘要
--------------------------------------------------
实验模式: 完整模式
样本数量: 全部
测试模型: TRANSFORMER
攻击类型数: 9

模型性能摘要
--------------------------------------------------

TRANSFORMER 模型:
  平均 Z-DR: 83.30%
  Z-DR 范围: [31.69%, 99.27%]
  论文基准: 90.00%
  差异: -6.70% (劣于论文)


零日攻击检测性能详细分析
--------------------------------------------------

攻击类型     | RF Z-DR      | MLP Z-DR     | 论文RF | 论文MLP | 状态
---------------------------------------------------------------------------
Exploits     | N/A          | N/A          | 94.43% | 90.31% | N/A
Fuzzers      | N/A          | N/A          | 14.77% | 20.10% | N/A
DoS          | N/A          | N/A          | 96.89% | 92.80% | N/A
Reconnaissance | N/A          | N/A          | 85.71% | 71.43% | N/A
Analysis     | N/A          | N/A          | 100.00% | 100.00% | N/A
Backdoor     | N/A          | N/A          | 100.00% | 100.00% | N/A
Shellcode    | N/A          | N/A          | 100.00% | 100.00% | N/A
Worms        | N/A          | N/A          | 100.00% | 100.00% | N/A
Generic      | N/A          | N/A          | 63.33% | 66.67% | N/A


结论
--------------------------------------------------

====================================================================================================