<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="15">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="thop" />
            <item index="2" class="java.lang.String" itemvalue="opencv-python" />
            <item index="3" class="java.lang.String" itemvalue="PyYAML" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="torchvision" />
            <item index="7" class="java.lang.String" itemvalue="tqdm" />
            <item index="8" class="java.lang.String" itemvalue="pandas" />
            <item index="9" class="java.lang.String" itemvalue="tensorboard" />
            <item index="10" class="java.lang.String" itemvalue="seaborn" />
            <item index="11" class="java.lang.String" itemvalue="matplotlib" />
            <item index="12" class="java.lang.String" itemvalue="pycocotools" />
            <item index="13" class="java.lang.String" itemvalue="Pillow" />
            <item index="14" class="java.lang.String" itemvalue="tensorflow" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>