"""
GPU环境设置和检查脚本
GPU Environment Setup and Check Script

检查系统GPU配置并安装必要的依赖包
"""

import subprocess
import sys
import os
import platform


def run_command(command, capture_output=True):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, shell=True, capture_output=capture_output, 
            text=True, check=True
        )
        return result.stdout.strip() if capture_output else None
    except subprocess.CalledProcessError as e:
        if capture_output:
            return f"Error: {e.stderr}"
        return None


def check_system_info():
    """检查系统信息"""
    print("🖥️ 系统信息检查")
    print("="*50)
    
    # 操作系统
    os_info = platform.platform()
    print(f"操作系统: {os_info}")
    
    # Python版本
    python_version = sys.version
    print(f"Python版本: {python_version}")
    
    # CPU信息
    if platform.system() == "Windows":
        cpu_info = run_command("wmic cpu get name")
        if cpu_info and "Error" not in cpu_info:
            cpu_name = cpu_info.split('\n')[1].strip()
            print(f"CPU: {cpu_name}")
    else:
        cpu_info = run_command("lscpu | grep 'Model name'")
        if cpu_info and "Error" not in cpu_info:
            print(f"CPU: {cpu_info.split(':')[1].strip()}")
    
    # 内存信息
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"内存: {memory.total / (1024**3):.1f} GB")
    except ImportError:
        print("内存: 无法检测 (需要安装psutil)")


def check_nvidia_gpu():
    """检查NVIDIA GPU"""
    print(f"\n🎮 NVIDIA GPU检查")
    print("="*50)
    
    # 检查nvidia-smi
    nvidia_smi = run_command("nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits")
    
    if nvidia_smi and "Error" not in nvidia_smi:
        print("✅ 检测到NVIDIA GPU:")
        lines = nvidia_smi.strip().split('\n')
        for i, line in enumerate(lines):
            parts = line.split(', ')
            if len(parts) >= 3:
                name, memory, driver = parts[0], parts[1], parts[2]
                print(f"   GPU {i}: {name}")
                print(f"           显存: {memory} MB")
                print(f"           驱动版本: {driver}")
        
        # 检查CUDA版本
        cuda_version = run_command("nvidia-smi | grep 'CUDA Version'")
        if cuda_version and "CUDA Version" in cuda_version:
            cuda_ver = cuda_version.split("CUDA Version: ")[1].split()[0]
            print(f"   CUDA版本: {cuda_ver}")
        
        return True
    else:
        print("❌ 未检测到NVIDIA GPU或驱动未安装")
        print("   请确保:")
        print("   1. 安装了NVIDIA显卡驱动")
        print("   2. nvidia-smi命令可用")
        return False


def check_pytorch():
    """检查PyTorch安装"""
    print(f"\n🔥 PyTorch检查")
    print("="*50)
    
    try:
        import torch
        print(f"✅ PyTorch已安装: {torch.__version__}")
        
        # 检查CUDA支持
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
            
            return True
        else:
            print("❌ PyTorch已安装但CUDA不可用")
            print("   可能需要安装CUDA版本的PyTorch")
            return False
            
    except ImportError:
        print("❌ PyTorch未安装")
        return False


def install_pytorch_cuda():
    """安装CUDA版本的PyTorch"""
    print(f"\n📦 安装CUDA版本的PyTorch")
    print("="*50)
    
    # 检测CUDA版本
    cuda_version = run_command("nvidia-smi | grep 'CUDA Version'")
    if cuda_version and "CUDA Version" in cuda_version:
        cuda_ver = cuda_version.split("CUDA Version: ")[1].split()[0]
        major_version = cuda_ver.split('.')[0]
        minor_version = cuda_ver.split('.')[1]
        
        print(f"检测到CUDA版本: {cuda_ver}")
        
        # 根据CUDA版本选择PyTorch安装命令
        if major_version == "12":
            install_cmd = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121"
        elif major_version == "11":
            install_cmd = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
        else:
            install_cmd = "pip install torch torchvision torchaudio"
            print(f"⚠️ 未知CUDA版本，使用默认安装命令")
        
        print(f"安装命令: {install_cmd}")
        
        # 询问用户是否安装
        response = input("是否现在安装? (y/n): ").lower().strip()
        if response == 'y':
            print("正在安装PyTorch...")
            result = run_command(install_cmd, capture_output=False)
            if result is not None:
                print("✅ PyTorch安装完成")
                return True
            else:
                print("❌ PyTorch安装失败")
                return False
        else:
            print("跳过PyTorch安装")
            return False
    else:
        print("❌ 无法检测CUDA版本")
        return False


def install_dependencies():
    """安装其他依赖包"""
    print(f"\n📦 安装其他依赖包")
    print("="*50)
    
    dependencies = [
        "numpy",
        "pandas", 
        "scikit-learn",
        "matplotlib",
        "seaborn",
        "plotly",
        "tqdm",
        "psutil"
    ]
    
    print("需要安装的包:")
    for dep in dependencies:
        print(f"  - {dep}")
    
    response = input("是否现在安装? (y/n): ").lower().strip()
    if response == 'y':
        install_cmd = f"pip install {' '.join(dependencies)}"
        print(f"安装命令: {install_cmd}")
        
        result = run_command(install_cmd, capture_output=False)
        if result is not None:
            print("✅ 依赖包安装完成")
            return True
        else:
            print("❌ 依赖包安装失败")
            return False
    else:
        print("跳过依赖包安装")
        return False


def test_gpu_training():
    """测试GPU训练"""
    print(f"\n🧪 GPU训练测试")
    print("="*50)
    
    try:
        import torch
        import torch.nn as nn
        import numpy as np
        
        # 检查设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        if device.type == 'cuda':
            print(f"GPU: {torch.cuda.get_device_name(0)}")
            print(f"显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        
        # 创建简单模型
        model = nn.Sequential(
            nn.Linear(10, 50),
            nn.ReLU(),
            nn.Linear(50, 2)
        ).to(device)
        
        # 创建测试数据
        X = torch.randn(1000, 10).to(device)
        y = torch.randint(0, 2, (1000,)).to(device)
        
        # 训练设置
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters())
        
        print("开始测试训练...")
        
        # 简单训练循环
        model.train()
        for epoch in range(10):
            optimizer.zero_grad()
            outputs = model(X)
            loss = criterion(outputs, y)
            loss.backward()
            optimizer.step()
            
            if epoch % 2 == 0:
                print(f"  Epoch {epoch}: Loss = {loss.item():.4f}")
        
        print("✅ GPU训练测试成功!")
        
        # 显存使用情况
        if device.type == 'cuda':
            allocated = torch.cuda.memory_allocated() / 1e6
            reserved = torch.cuda.memory_reserved() / 1e6
            print(f"显存使用: {allocated:.1f} MB (已分配), {reserved:.1f} MB (已保留)")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU训练测试失败: {e}")
        return False


def generate_training_script():
    """生成训练脚本"""
    print(f"\n📝 生成训练脚本")
    print("="*50)
    
    script_content = """#!/bin/bash
# GPU训练启动脚本

echo "🚀 启动完整数据集GPU训练"

# 检查数据文件
if [ ! -f "data/UNSW/UNSW_NB15_training-set.csv" ]; then
    echo "❌ 训练集文件不存在: data/UNSW/UNSW_NB15_training-set.csv"
    exit 1
fi

if [ ! -f "data/UNSW/UNSW_NB15_testing-set.csv" ]; then
    echo "❌ 测试集文件不存在: data/UNSW/UNSW_NB15_testing-set.csv"
    exit 1
fi

# 运行完整训练
echo "开始MLP模型完整训练..."
python full_training.py --models mlp --epochs 50 --batch-size 2048

echo "开始RF模型完整训练..."
python full_training.py --models rf

echo "开始MLP+RF对比训练..."
python full_training.py --models mlp,rf --epochs 30 --batch-size 1024

echo "✅ 完整训练完成!"
"""
    
    # Windows版本
    bat_content = """@echo off
REM GPU训练启动脚本

echo 🚀 启动完整数据集GPU训练

REM 检查数据文件
if not exist "data\\UNSW\\UNSW_NB15_training-set.csv" (
    echo ❌ 训练集文件不存在: data\\UNSW\\UNSW_NB15_training-set.csv
    exit /b 1
)

if not exist "data\\UNSW\\UNSW_NB15_testing-set.csv" (
    echo ❌ 测试集文件不存在: data\\UNSW\\UNSW_NB15_testing-set.csv
    exit /b 1
)

REM 运行完整训练
echo 开始MLP模型完整训练...
python full_training.py --models mlp --epochs 50 --batch-size 2048

echo 开始RF模型完整训练...
python full_training.py --models rf

echo 开始MLP+RF对比训练...
python full_training.py --models mlp,rf --epochs 30 --batch-size 1024

echo ✅ 完整训练完成!
pause
"""
    
    # 保存脚本
    with open('run_full_training.sh', 'w', encoding='utf-8') as f:
        f.write(script_content)

    with open('run_full_training.bat', 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    # 设置执行权限 (Linux/Mac)
    if platform.system() != "Windows":
        os.chmod('run_full_training.sh', 0o755)
    
    print("✅ 训练脚本已生成:")
    print("   - run_full_training.sh (Linux/Mac)")
    print("   - run_full_training.bat (Windows)")


def main():
    """主函数"""
    print("🔧 GPU环境设置和检查")
    print("="*80)
    
    # 系统信息检查
    check_system_info()
    
    # GPU检查
    gpu_available = check_nvidia_gpu()
    
    # PyTorch检查
    pytorch_cuda_available = check_pytorch()
    
    # 如果GPU可用但PyTorch不支持CUDA，提供安装选项
    if gpu_available and not pytorch_cuda_available:
        install_pytorch_cuda()
        # 重新检查
        pytorch_cuda_available = check_pytorch()
    
    # 安装其他依赖
    install_dependencies()
    
    # 如果一切就绪，进行GPU训练测试
    if pytorch_cuda_available:
        test_gpu_training()
    
    # 生成训练脚本
    generate_training_script()
    
    # 总结
    print(f"\n📋 环境检查总结")
    print("="*50)
    print(f"NVIDIA GPU: {'✅ 可用' if gpu_available else '❌ 不可用'}")
    print(f"PyTorch CUDA: {'✅ 可用' if pytorch_cuda_available else '❌ 不可用'}")
    
    if gpu_available and pytorch_cuda_available:
        print(f"\n🎉 GPU环境配置完成!")
        print(f"现在可以运行完整数据集训练:")
        print(f"  python full_training.py --models mlp --epochs 50")
        print(f"  或使用生成的脚本: ./run_full_training.sh")
    else:
        print(f"\n⚠️ GPU环境未完全配置")
        print(f"建议:")
        if not gpu_available:
            print(f"  1. 安装NVIDIA显卡驱动")
            print(f"  2. 安装CUDA Toolkit")
        if not pytorch_cuda_available:
            print(f"  3. 安装CUDA版本的PyTorch")


if __name__ == '__main__':
    main()
