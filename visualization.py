"""
Advanced Visualization Module for Zero-Shot Learning NIDS

This module implements comprehensive visualization components for:
"From Zero-Shot Machine Learning to Zero-Day Attack Detection"
International Journal of Information Security, 2023

Key Visualizations:
1. Attack type distribution analysis
2. Zero-day detection rate comparison charts
3. Confusion matrix heatmaps
4. ROC curves and performance analysis
5. Model performance radar charts
6. Statistical analysis plots
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import roc_curve, auc, confusion_matrix
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo
from datetime import datetime
import os
import json


class ZeroShotVisualizer:
    """
    Advanced visualization framework for zero-shot learning NIDS
    
    This class creates publication-quality visualizations for experimental results,
    performance analysis, and model comparisons.
    """
    
    def __init__(self, figures_dir='figures', interactive=True, session_timestamp=None):
        """
        Initialize the visualizer

        Args:
            figures_dir (str): Directory to save figures
            interactive (bool): Whether to create interactive plots
            session_timestamp (str): Optional session timestamp for organizing files
        """
        self.base_figures_dir = figures_dir
        self.interactive = interactive

        # Create session-specific subdirectory
        if session_timestamp is None:
            session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.session_timestamp = session_timestamp
        self.figures_dir = os.path.join(figures_dir, f"session_{session_timestamp}")

        os.makedirs(self.figures_dir, exist_ok=True)

        # Set style for publication-quality plots
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        # Color schemes for different components
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'info': '#7209B7',
            'attack_colors': px.colors.qualitative.Set3
        }

        # Generate timestamp for file naming
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Paper baseline results for comparison
        self.paper_baselines = {
            'rf': {
                'Exploits': 94.43, 'Fuzzers': 14.77, 'DoS': 96.89,
                'Reconnaissance': 85.71, 'Analysis': 100.0, 'Backdoor': 100.0,
                'Shellcode': 100.0, 'Worms': 100.0, 'Generic': 63.33,
                'avg_z_dr': 80.67
            },
            'mlp': {
                'Exploits': 90.31, 'Fuzzers': 20.10, 'DoS': 92.80,
                'Reconnaissance': 71.43, 'Analysis': 100.0, 'Backdoor': 100.0,
                'Shellcode': 100.0, 'Worms': 100.0, 'Generic': 66.67,
                'avg_z_dr': 82.37
            }
        }
    
    def plot_attack_distribution(self, data_stats, save_path=None):
        """
        Create attack type distribution visualization
        
        Args:
            data_stats (dict): Dataset statistics
            save_path (str): Optional path to save the figure
            
        Returns:
            str: Path to saved figure
        """
        if self.interactive:
            return self._plot_attack_distribution_interactive(data_stats, save_path)
        else:
            return self._plot_attack_distribution_static(data_stats, save_path)
    
    def _plot_attack_distribution_static(self, data_stats, save_path=None):
        """Create static attack distribution plot"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Extract attack type counts
        attack_types = list(data_stats.keys())
        counts = list(data_stats.values())
        
        # Pie chart
        ax1.pie(counts, labels=attack_types, autopct='%1.1f%%', startangle=90)
        ax1.set_title('Attack Type Distribution', fontsize=14, fontweight='bold')
        
        # Bar chart
        bars = ax2.bar(attack_types, counts, color=self.colors['attack_colors'][:len(attack_types)])
        ax2.set_title('Attack Type Counts', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Attack Type')
        ax2.set_ylabel('Number of Samples')
        ax2.tick_params(axis='x', rotation=45)
        
        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height):,}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        if save_path is None:
            save_path = os.path.join(self.figures_dir, 'attack_distribution.png')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def _plot_attack_distribution_interactive(self, data_stats, save_path=None):
        """Create interactive attack distribution plot"""
        attack_types = list(data_stats.keys())
        counts = list(data_stats.values())
        
        # Create subplots
        fig = make_subplots(
            rows=1, cols=2,
            specs=[[{"type": "pie"}, {"type": "bar"}]],
            subplot_titles=('Attack Type Distribution', 'Attack Type Counts')
        )
        
        # Pie chart
        fig.add_trace(
            go.Pie(labels=attack_types, values=counts, name="Distribution"),
            row=1, col=1
        )
        
        # Bar chart
        fig.add_trace(
            go.Bar(x=attack_types, y=counts, name="Counts",
                  marker_color=self.colors['attack_colors'][:len(attack_types)]),
            row=1, col=2
        )
        
        fig.update_layout(
            title_text="UNSW-NB15 Dataset Attack Type Analysis",
            title_x=0.5,
            showlegend=False,
            height=500
        )
        
        if save_path is None:
            save_path = os.path.join(self.figures_dir, 'attack_distribution.html')

        fig.write_html(save_path)
        
        return save_path
    
    def plot_zdr_comparison(self, experiment_results, model_types=['rf', 'mlp'], save_path=None):
        """
        Create zero-day detection rate comparison visualization
        
        Args:
            experiment_results (dict): Experiment results for all models
            model_types (list): List of model types to compare
            save_path (str): Optional path to save the figure
            
        Returns:
            str: Path to saved figure
        """
        if self.interactive:
            return self._plot_zdr_comparison_interactive(experiment_results, model_types, save_path)
        else:
            return self._plot_zdr_comparison_static(experiment_results, model_types, save_path)
    
    def _plot_zdr_comparison_static(self, experiment_results, model_types, save_path=None):
        """Create static Z-DR comparison plot"""
        fig, ax = plt.subplots(figsize=(14, 8))
        
        attack_types = []
        model_data = {model: [] for model in model_types}
        paper_data = {model: [] for model in model_types}
        
        # Extract data for all attack types
        for model_type in model_types:
            if model_type in experiment_results:
                for attack_type, result in experiment_results[model_type].items():
                    if attack_type not in attack_types:
                        attack_types.append(attack_type)
                    
                    if 'z_dr' in result:
                        model_data[model_type].append(result['z_dr'])
                    else:
                        model_data[model_type].append(0)
                    
                    # Add paper baseline
                    if (model_type in self.paper_baselines and 
                        attack_type in self.paper_baselines[model_type]):
                        paper_data[model_type].append(self.paper_baselines[model_type][attack_type])
                    else:
                        paper_data[model_type].append(0)
        
        # Create grouped bar chart
        x = np.arange(len(attack_types))
        width = 0.2
        
        for i, model_type in enumerate(model_types):
            offset = (i - len(model_types)/2 + 0.5) * width
            
            # Our results
            ax.bar(x + offset, model_data[model_type], width, 
                  label=f'{model_type.upper()} (Ours)', alpha=0.8)
            
            # Paper baseline
            ax.bar(x + offset + width, paper_data[model_type], width,
                  label=f'{model_type.upper()} (Paper)', alpha=0.6, hatch='//')
        
        ax.set_xlabel('Attack Type')
        ax.set_ylabel('Zero-day Detection Rate (%)')
        ax.set_title('Zero-day Detection Rate Comparison: Our Results vs Paper Baseline')
        ax.set_xticks(x)
        ax.set_xticklabels(attack_types, rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path is None:
            models_str = "_".join(model_types)
            save_path = os.path.join(self.figures_dir, f'zdr_comparison_{models_str}.png')

        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def _plot_zdr_comparison_interactive(self, experiment_results, model_types, save_path=None):
        """Create interactive Z-DR comparison plot"""
        fig = go.Figure()
        
        attack_types = []
        
        # Collect all attack types
        for model_type in model_types:
            if model_type in experiment_results:
                attack_types.extend(experiment_results[model_type].keys())
        
        attack_types = sorted(list(set(attack_types)))
        
        for model_type in model_types:
            our_results = []
            paper_results = []
            
            for attack_type in attack_types:
                # Our results
                if (model_type in experiment_results and 
                    attack_type in experiment_results[model_type] and
                    'z_dr' in experiment_results[model_type][attack_type]):
                    our_results.append(experiment_results[model_type][attack_type]['z_dr'])
                else:
                    our_results.append(0)
                
                # Paper baseline
                if (model_type in self.paper_baselines and 
                    attack_type in self.paper_baselines[model_type]):
                    paper_results.append(self.paper_baselines[model_type][attack_type])
                else:
                    paper_results.append(0)
            
            # Add our results
            fig.add_trace(go.Bar(
                name=f'{model_type.upper()} (Ours)',
                x=attack_types,
                y=our_results,
                marker_color=self.colors['primary'] if model_type == 'rf' else self.colors['secondary']
            ))
            
            # Add paper baseline
            fig.add_trace(go.Bar(
                name=f'{model_type.upper()} (Paper)',
                x=attack_types,
                y=paper_results,
                marker_color=self.colors['primary'] if model_type == 'rf' else self.colors['secondary'],
                opacity=0.6,
                marker_pattern_shape="/"
            ))
        
        fig.update_layout(
            title='Zero-day Detection Rate Comparison: Our Results vs Paper Baseline',
            xaxis_title='Attack Type',
            yaxis_title='Zero-day Detection Rate (%)',
            barmode='group',
            height=600,
            hovermode='x unified'
        )
        
        if save_path is None:
            models_str = "_".join(model_types)
            save_path = os.path.join(self.figures_dir, f'zdr_comparison_{models_str}.html')
        
        fig.write_html(save_path)
        
        return save_path
    
    def plot_confusion_matrix(self, y_true, y_pred, attack_type, model_type, save_path=None):
        """
        Create confusion matrix heatmap
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            attack_type (str): Attack type being analyzed
            model_type (str): Model type
            save_path (str): Optional path to save the figure
            
        Returns:
            str: Path to saved figure
        """
        # Calculate confusion matrix
        cm = confusion_matrix(y_true, y_pred)
        
        # Create heatmap
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=['Benign', 'Attack'],
                   yticklabels=['Benign', 'Attack'])
        
        plt.title(f'Confusion Matrix: {model_type.upper()} - {attack_type} as Zero-day')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        
        if save_path is None:
            save_path = os.path.join(self.figures_dir, f'confusion_matrix_{model_type}_{attack_type}.png')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def plot_roc_curves(self, experiment_results, model_types=['rf', 'mlp'], save_path=None):
        """
        Create ROC curves for model comparison
        
        Args:
            experiment_results (dict): Experiment results with prediction probabilities
            model_types (list): List of model types to compare
            save_path (str): Optional path to save the figure
            
        Returns:
            str: Path to saved figure
        """
        plt.figure(figsize=(10, 8))
        
        for model_type in model_types:
            if model_type in experiment_results:
                # This would need actual prediction probabilities from experiments
                # For now, create a placeholder
                fpr = np.linspace(0, 1, 100)
                tpr = np.power(fpr, 0.5)  # Placeholder curve
                roc_auc = auc(fpr, tpr)
                
                plt.plot(fpr, tpr, linewidth=2, 
                        label=f'{model_type.upper()} (AUC = {roc_auc:.3f})')
        
        plt.plot([0, 1], [0, 1], 'k--', linewidth=1, label='Random Classifier')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curves: Model Comparison')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        
        if save_path is None:
            save_path = os.path.join(self.figures_dir, 'roc_curves.png')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return save_path

    def plot_performance_radar(self, experiment_results, model_types=['rf', 'mlp'], save_path=None):
        """
        Create radar chart for model performance comparison

        Args:
            experiment_results (dict): Experiment results
            model_types (list): List of model types to compare
            save_path (str): Optional path to save the figure

        Returns:
            str: Path to saved figure
        """
        if self.interactive:
            return self._plot_performance_radar_interactive(experiment_results, model_types, save_path)
        else:
            return self._plot_performance_radar_static(experiment_results, model_types, save_path)

    def _plot_performance_radar_static(self, experiment_results, model_types, save_path=None):
        """Create static radar chart"""
        # Calculate average metrics for each model
        metrics = ['Z-DR', 'Accuracy', 'Precision', 'Recall', 'F1', 'AUC']

        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle

        for model_type in model_types:
            if model_type in experiment_results:
                values = []

                # Calculate average metrics
                all_results = experiment_results[model_type]
                successful_results = [r for r in all_results.values() if 'z_dr' in r]

                if successful_results:
                    avg_z_dr = np.mean([r['z_dr'] for r in successful_results])
                    avg_accuracy = np.mean([r.get('accuracy', 0) * 100 for r in successful_results])
                    avg_precision = np.mean([r.get('precision', 0) * 100 for r in successful_results])
                    avg_recall = np.mean([r.get('recall', 0) * 100 for r in successful_results])
                    avg_f1 = np.mean([r.get('f1', 0) * 100 for r in successful_results])
                    avg_auc = np.mean([r.get('auc', 0) * 100 for r in successful_results])

                    values = [avg_z_dr, avg_accuracy, avg_precision, avg_recall, avg_f1, avg_auc]
                    values += values[:1]  # Complete the circle

                    ax.plot(angles, values, 'o-', linewidth=2, label=f'{model_type.upper()}')
                    ax.fill(angles, values, alpha=0.25)

        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 100)
        ax.set_title('Model Performance Comparison (Radar Chart)', size=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)

        if save_path is None:
            models_str = "_".join(model_types)
            save_path = os.path.join(self.figures_dir, f'performance_radar_{models_str}.png')

        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        return save_path

    def _plot_performance_radar_interactive(self, experiment_results, model_types, save_path=None):
        """Create interactive radar chart"""
        fig = go.Figure()

        metrics = ['Z-DR', 'Accuracy', 'Precision', 'Recall', 'F1', 'AUC']

        for model_type in model_types:
            if model_type in experiment_results:
                all_results = experiment_results[model_type]
                successful_results = [r for r in all_results.values() if 'z_dr' in r]

                if successful_results:
                    avg_z_dr = np.mean([r['z_dr'] for r in successful_results])
                    avg_accuracy = np.mean([r.get('accuracy', 0) * 100 for r in successful_results])
                    avg_precision = np.mean([r.get('precision', 0) * 100 for r in successful_results])
                    avg_recall = np.mean([r.get('recall', 0) * 100 for r in successful_results])
                    avg_f1 = np.mean([r.get('f1', 0) * 100 for r in successful_results])
                    avg_auc = np.mean([r.get('auc', 0) * 100 for r in successful_results])

                    values = [avg_z_dr, avg_accuracy, avg_precision, avg_recall, avg_f1, avg_auc]

                    fig.add_trace(go.Scatterpolar(
                        r=values,
                        theta=metrics,
                        fill='toself',
                        name=f'{model_type.upper()}',
                        line_color=self.colors['primary'] if model_type == 'rf' else self.colors['secondary']
                    ))

        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )),
            showlegend=True,
            title="Model Performance Comparison (Radar Chart)",
            height=600
        )

        if save_path is None:
            models_str = "_".join(model_types)
            save_path = os.path.join(self.figures_dir, f'performance_radar_{models_str}.html')

        fig.write_html(save_path)

        return save_path

    def generate_single_model_visualizations(self, experiment_results, model_type, data_stats=None):
        """
        Generate visualizations for a single model

        Args:
            experiment_results (dict): Experiment results for the model
            model_type (str): Model type (e.g., 'rf', 'mlp')
            data_stats (dict): Dataset statistics

        Returns:
            dict: Paths to generated visualization files
        """
        print(f"🎨 为 {model_type.upper()} 模型生成可视化图表...")

        generated_files = {}

        try:
            # 1. 单模型性能雷达图
            print(f"🎯 生成 {model_type.upper()} 性能雷达图...")
            radar_path = os.path.join(self.figures_dir, f'performance_radar_{model_type}.html')
            generated_files['performance_radar'] = self.plot_performance_radar(
                {model_type: experiment_results},
                model_types=[model_type],
                save_path=radar_path
            )

            # 2. 单模型Z-DR比较图（与论文基准对比）
            print(f"📈 生成 {model_type.upper()} Z-DR对比图...")
            zdr_path = os.path.join(self.figures_dir, f'zdr_comparison_{model_type}.html')
            generated_files['zdr_comparison'] = self.plot_zdr_comparison(
                {model_type: experiment_results},
                model_types=[model_type],
                save_path=zdr_path
            )

            print(f"✅ {model_type.upper()} 模型可视化完成，生成 {len(generated_files)} 个图表文件")
            print(f"📁 保存位置: {self.figures_dir}/")

            return generated_files

        except Exception as e:
            print(f"❌ 生成 {model_type.upper()} 模型可视化时出错: {e}")
            return generated_files

    def generate_all_visualizations(self, experiment_results, cv_results=None, data_stats=None):
        """
        Generate all visualization components

        Args:
            experiment_results (dict): Complete experiment results
            cv_results (dict): Cross-validation results
            data_stats (dict): Dataset statistics

        Returns:
            dict: Paths to all generated visualizations
        """
        print("🎨 Generating comprehensive visualizations...")

        generated_files = {}

        try:
            # 1. Attack distribution
            if data_stats:
                print("📊 Creating attack distribution plots...")
                generated_files['attack_distribution'] = self.plot_attack_distribution(data_stats)

            # 2. Z-DR comparison
            print("📈 Creating Z-DR comparison charts...")
            generated_files['zdr_comparison'] = self.plot_zdr_comparison(experiment_results)

            # 3. Performance radar
            print("🎯 Creating performance radar charts...")
            generated_files['performance_radar'] = self.plot_performance_radar(experiment_results)

            print(f"✅ Generated {len(generated_files)} visualization files")
            print(f"📁 Saved to: {self.figures_dir}/")

            return generated_files

        except Exception as e:
            print(f"❌ Error generating visualizations: {e}")
            return generated_files


def main():
    """
    Test the visualization module
    """
    print("🎨 Testing Zero-Shot Learning NIDS Visualization Module")
    print("="*60)

    # Initialize visualizer
    visualizer = ZeroShotVisualizer()

    # Generate sample data for testing
    sample_data_stats = {
        'Normal': 50000, 'Exploits': 15000, 'DoS': 8000, 'Fuzzers': 5000,
        'Reconnaissance': 3000, 'Analysis': 2000, 'Backdoor': 1500,
        'Shellcode': 1000, 'Worms': 500, 'Generic': 2000
    }

    sample_experiment_results = {
        'rf': {
            'Exploits': {'z_dr': 98.9, 'accuracy': 0.999, 'precision': 0.998, 'recall': 0.999, 'f1': 0.998, 'auc': 0.999},
            'DoS': {'z_dr': 99.6, 'accuracy': 0.997, 'precision': 0.996, 'recall': 0.997, 'f1': 0.997, 'auc': 0.998},
            'Fuzzers': {'z_dr': 64.7, 'accuracy': 0.985, 'precision': 0.980, 'recall': 0.985, 'f1': 0.982, 'auc': 0.990}
        },
        'mlp': {
            'Exploits': {'z_dr': 95.2, 'accuracy': 0.996, 'precision': 0.995, 'recall': 0.996, 'f1': 0.995, 'auc': 0.997},
            'DoS': {'z_dr': 97.8, 'accuracy': 0.994, 'precision': 0.993, 'recall': 0.994, 'f1': 0.993, 'auc': 0.996},
            'Fuzzers': {'z_dr': 68.5, 'accuracy': 0.982, 'precision': 0.978, 'recall': 0.982, 'f1': 0.980, 'auc': 0.988}
        }
    }

    # Test visualization generation
    print("\n🎨 Testing visualization generation...")
    generated_files = visualizer.generate_all_visualizations(
        sample_experiment_results,
        data_stats=sample_data_stats
    )

    print(f"\n✅ Visualization module testing completed!")
    print(f"Generated files: {list(generated_files.keys())}")


if __name__ == '__main__':
    main()
